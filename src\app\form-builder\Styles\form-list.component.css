/* Main container */
.list-manager-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f8fafc;
  font-family: '<PERSON><PERSON><PERSON>';
}

/* Sidebar styles */
.sidebar {
  width: 280px;
  background-color: #2c6ddd;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}
/* .sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1;
} */
.sidebar-header {
  padding: 8px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background-color: #283fc0;
  position: relative;
  z-index: 2;
  margin: 0;
  font-size: 15px;
  display: flex;
  font-weight: 600;
  gap: 6vw;
  align-items: center;
}
.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  backdrop-filter: blur(10px);
}
.header-icon i {
  font-size: 20px;
  color: white;
}
.sidebar-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.sidebar-content {
  padding: 20px 0;
  position: relative;
  z-index: 2;
}
.sidebar-nav {
  padding: 0px 0px;
}
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.nav-link {
  display: flex;
  align-items: center;
  padding: 5px 15px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.nav-item {
  margin-bottom: 0px;
}
.nav-text {
  flex: 1;
  font-size: 12px;
}

.list-items {
  list-style-type: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex: 1;
}

.list-item {
  padding: 6px 10px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.list-item:hover {
  background-color: #f5f5f5;
}

.list-item.active {
  background-color: #e6f2ff;
  border-left: 3px solid #007bff;
  font-weight: 500;
}

.list-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-list-message {
  padding: 10px;
  color: white;
  text-align: center;
  font-style: italic;
  font-size: 11px;
}

/* Right panel styles */
.right-panel {
  flex: 1;
  padding: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h2 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 6px;
}

.add-item-btn, .save-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-item-btn {
  background-color: #28a745;
  color: white;
}

.add-item-btn:hover {
  background-color: #218838;
}

.save-btn {
  background-color: #007bff;
  color: white;
}

.save-btn:hover {
  background-color: #0069d9;
}

/* Item list styles */
.item-list {
  padding: 8px;
  overflow-y: auto;
  flex: 1;
}

.item-card {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  margin-bottom: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.item-title h3 {
  margin: 0;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-hint {
  font-size: 10px;
  color: #6c757d;
  font-weight: normal;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 3px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.add-btn {
  color: #28a745;
}

.add-btn:hover {
  background-color: rgba(40, 167, 69, 0.1);
}

.delete-btn {
  color: #dc3545;
}

.delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.editable-input {
  width: 100%;
  padding: 4px 6px;
  font-size: 13px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  outline: none;
}

.editable-input:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Item values styles */
.item-values {
  padding: 6px;
}

.value-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  gap: 4px;
}

.value-input {
  flex: 1;
  padding: 4px 6px;
  border: none;
  border-bottom: 1px solid #ced4da;
  border-radius: 3px;
  font-size: 12px;
  transition: border-color 0.2s;
}

.value-input:focus {
  border-color: #80bdff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.remove-value-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 14px;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.remove-value-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.empty-values-message, .empty-items-message {
  color: #6c757d;
  text-align: center;
  padding: 8px;
  font-style: italic;
  font-size: 11px;
}

/* Empty state styles */
.right-panel.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
}

.empty-message {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 32px;
  color: #adb5bd;
  margin-bottom: 10px;
}

.empty-message h3 {
  margin: 0 0 5px 0;
  font-size: 15px;
  font-weight: 500;
}

.empty-message p {
  margin: 0;
  font-size: 12px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: 6px;
  width: 350px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  line-height: 1;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
}

.close-btn:hover {
  color: #343a40;
}

.modal-body {
  padding: 12px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
  font-size: 12px;
}

.input-field {
  width: 100%;
  padding: 6px 8px;
  font-size: 12px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #80bdff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 10px 12px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.create-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-weight: 500;
  font-size: 12px;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #0069d9;
}

.create-btn:disabled {
  background-color: #a9c9f5;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-weight: 500;
  font-size: 12px;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

