<header class="app-header" *ngIf="!isFormPreviewRoute">
  <nav class="navbar">
    <!-- Left section: Logo (click)="hideDashboard()" -->
    <div class="navbar-left">
      <div ><img class="logo" routerLink="/form-view" (click)="hideDashboard()" src="assets/Images/Formsflow.png" alt="Logo"></div>
    </div>

    <!-- Middle section: Location Button -->
    <div class="navbar-middle" *ngIf="isLoggedIn && !isFormEditMode">
      <!-- Middle section can be used for other content or left empty -->
    </div>

    <!-- Mobile Menu Toggle Button -->
    <div class="mobile-menu-toggle" (click)="toggleMobileMenu()">
      <i class="fas" [ngClass]="{'fa-bars': !isMobileMenuOpen, 'fa-times': isMobileMenuOpen}"></i>
    </div>

    <!-- Right section: Navigation links (Desktop) -->
    <div class="navbar-right">
      <ul class="nav-links">
        <!-- Show login/signup links if not logged in -->
        <!-- <ng-container *ngIf="!isLoggedIn">
          <li><a routerLink="/auth/login">Login</a></li>
          <li><a routerLink="/auth/signup">Sign Up</a></li>
        </ng-container> -->

        <!-- Location Dropdown -->
        <li class="location-dropdown-item" *ngIf="isLoggedIn && !isFormEditMode">
          <div class="location-dropdown">
            <div class="location-selector" (click)="openLocationbyUSer()">
              <i class="fas fa-map-marker-alt"></i>
              <span class="location-text">{{ this.shareServices.selectedLocation }}</span>
              <i class="fas fa-chevron-down location-arrow"></i>
            </div>
          </div>
        </li>

        <!-- Show user profile if logged in -->
        <li class="user-profile" *ngIf="isLoggedIn && !isFormEditMode">
          <div class="user-info">
            <i class="fas fa-user"></i> {{ currentUser?.firstName || 'Dev One' }}

          </div>
          <div class="logout-dropdown">
            <div class="dropdown-item">
              <i class="fas fa-user-circle"></i> My Profile
            </div>
            <div class="dropdown-item">
              <i class="fas fa-cog"></i> Settings
            </div>
            <div class="dropdown-divider"></div>
            <div class="dropdown-item logout" (click)="logout()">
              <i class="fas fa-sign-out-alt"></i> Logout
            </div>
          </div>
        </li>
      </ul>
    </div>
  </nav>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay" [class.open]="isMobileMenuOpen">
    <div class="mobile-menu-content">
      <div class="mobile-menu-header">
        <div class="mobile-user-info" *ngIf="isLoggedIn && !isFormEditMode">
          <i class="fas fa-user-circle"></i>
          <span>{{ currentUser?.firstName || 'User' }}</span>
        </div>
        <div class="mobile-user-info" *ngIf="!isLoggedIn">
          <i class="fas fa-user-circle"></i>
          <span>Dev One</span>
        </div>
        <button type="button" class="mobile-menu-close" (click)="toggleMobileMenu()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mobile-menu-body">
        <ng-container *ngIf="isLoggedIn">
        <ul class="mobile-nav-links">
          <!-- Auth links for non-logged in users -->
          <!-- <ng-container *ngIf="!isLoggedIn">
            <li><a routerLink="/auth/login" (click)="closeMobileMenu()">Login</a></li>
            <li><a routerLink="/auth/signup" (click)="closeMobileMenu()">Sign Up</a></li>
            <li class="divider"></li>
          </ng-container> -->

          <!-- App links for logged in users -->
            <li><a routerLink="/build/form" (click)="closeMobileMenu()">Build Form</a></li>
            <li><a routerLink="/build/List" (click)="closeMobileMenu()">Form List</a></li>
            <li class="divider"></li>
            <li><a href="#"><i class="fas fa-user-circle"></i> My Profile</a></li>
            <li><a href="#"><i class="fas fa-cog"></i> Settings</a></li>
            <li><a (click)="logout()" class="logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
          </ul>
          </ng-container>
      </div>
    </div>
  </div>

  <!-- Location Popup -->
  <div class="popup-overlay" *ngIf="this.shareServices.showLocationPopup" >
    <div class="location-popup-container" (click)="$event.stopPropagation()">
      <div class="popup-header">
        <h3>Select Location</h3>
        <!-- <button class="close-btn" (click)="closeLocationPopup()">×</button> -->
      </div>
      <div class="popup-content">
        <div class="location-list">
          <div
            *ngFor="let location of selectionList"
            class="location-item"
            [class.selected]="location === shareServices.selectedLocation"
            (click)="shareServices.selectedLocation = location; onLocationChange()">
            <div class="location-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="location-name">{{ location }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<div class="app-container">
  <!-- Left Sidebar - Only show when logged in and not on form-preview route -->
  <aside class="left-sidebar" *ngIf="isLoggedIn && !isFormPreviewRoute && !isFormEditMode">
    <div class="sidebar-content">
      <ul class="sidebar-nav">
        <li title="Dashboard">
          <a routerLinkActive="active" (click)="showdashboard()">
            <i class="fas fa-tachometer-alt" ></i>
            <span>Dashboard</span>
          </a>
        </li>
        <li title="Form config">
          <a routerLink="/form/config" routerLinkActive="active">
            <i class="fa-solid fa-toolbox" title="Form config"></i>
            <span>Form config</span>
          </a>
        </li>
        <li title="Company Setting">
          <a routerLink="/Company/integration" routerLinkActive="active">
            <i class="fas fa-cog" title="Company Setting"></i>
            <span>Company Setting</span>
          </a>
        </li>
        <li title="Locations">
          <a routerLink="/Locations" routerLinkActive="active">
            <i class="fas fa-map-marker-alt" title="Locations"></i>
            <span>Locations</span>
          </a>
        </li>
        <li title="Users">
          <a routerLink="/Users" routerLinkActive="active">
            <i class="fas fa-users" title="Users"></i>
            <span>Users</span>
          </a>
        </li>
      </ul>
    </div>
  </aside>

  <!-- Main Content - Adjust width based on login status and route -->
  <main class="main-content" [ngClass]="{'full-width': !isLoggedIn || isFormPreviewRoute || isFormEditMode}">
    <router-outlet></router-outlet>
  </main>
</div>

<!-- #DashBoard -->
