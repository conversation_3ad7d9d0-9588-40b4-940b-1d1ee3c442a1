.form-html-view-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.actions-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.action-button {
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #0b7dda;
}

.action-button i {
  font-size: 16px;
}

.form-content {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.form-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.logo-container {
  flex: 0 0 150px;
  margin-right: 20px;
}

.header-logo {
  max-width: 100%;
  height: auto;
}

.header-text {
  flex: 1;
}

.form-title {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #333;
}

.form-subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Section styles */
.form-section {
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.no-sections-message {
  background-color: #f9f9f9;
  padding: 24px;
  text-align: center;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 24px;
}

.no-sections-message p {
  color: #666;
  font-style: italic;
  margin: 0;
  font-size: 15px;
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.section-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: white;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Table styles */
.form-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-table th,
.form-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.form-table tr:last-child td {
  border-bottom: none;
}

.form-table tr:hover {
  background-color: #fafafa;
  transition: background-color 0.2s ease;
}

.form-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-column {
  width: 35%;
  min-width: 200px;
}

.value-column {
  width: 65%;
}

.field-name {
  font-weight: 600;
  color: #444;
  vertical-align: top;
  font-size: 14px;
  line-height: 1.4;
  padding-right: 20px;
}

.field-value {
  color: #333;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
  min-height: 20px;
  display: flex;
  align-items: flex-start;
}

/* Image row styles */
.image-row td {
  padding-top: 20px;
  padding-bottom: 20px;
}

.form-image,
.signature-image {
  max-width: 100%;
  max-height: 250px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.form-image:hover,
.signature-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.signature-image {
  max-height: 120px;
  background-color: #fafafa;
}

.no-image,
.no-value {
  color: #999;
  font-style: italic;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #ddd;
}

/* Textarea content */
.textarea-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-height: 80px;
  max-height: 250px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Object content */
.object-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  max-height: 250px;
  overflow-y: auto;
  margin: 0;
  color: #444;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Signature section */
.signature-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

/* Field type specific styles */
.field-value ul {
  margin: 0;
  padding-left: 24px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px 12px 12px 32px;
  border-left: 3px solid #007bff;
}

.field-value li {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.field-value li:last-child {
  margin-bottom: 0;
}

/* Boolean values */
.boolean-value {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.boolean-value.true {
  color: #2e7d32;
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
}

.boolean-value.false {
  color: #c62828;
  background-color: #ffebee;
  border: 1px solid #f44336;
}

.boolean-value i {
  margin-right: 6px;
  font-size: 14px;
}

.signature-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

/* Related fields styles */
.related-field-row {
  background-color: #f8f9fa;
  border-left: 3px solid #007bff;
}

.related-field-row:hover {
  background-color: #f0f0f0;
}

.related-field-name {
  font-weight: 500;
  color: #666;
  font-size: 13px;
  padding-left: 30px;
}

.related-field-name .indent {
  color: #007bff;
  font-weight: 600;
}

.related-field-value {
  color: #555;
  font-size: 13px;
  padding-left: 10px;
}

.flag-value {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.comment-value {
  background-color: #e3f2fd;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #2196f3;
  font-style: italic;
  color: #1565c0;
  white-space: pre-wrap;
  max-width: 100%;
  word-break: break-word;
}

.related-image {
  max-width: 200px;
  max-height: 150px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.related-image:hover {
  transform: scale(1.05);
}

/* Responsive styles */
@media (max-width: 767px) {
  .form-html-view-container {
    padding: 15px;
  }

  .form-content {
    padding: 20px;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .logo-container {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .form-title {
    font-size: 20px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .section-header {
    padding: 8px 12px;
  }

  .section-header h3 {
    font-size: 15px;
  }

  .form-table th,
  .form-table td {
    padding: 10px;
  }

  /* Stack field name and value on mobile */
  .form-table tr {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #eee;
    padding: 8px 0;
  }

  .form-table tr:last-child {
    border-bottom: none;
  }

  .form-table td {
    border-bottom: none;
    padding: 5px 10px;
  }

  .field-name {
    font-weight: 600;
    color: #666;
    font-size: 13px;
  }

  .field-column,
  .value-column {
    width: auto;
  }

  /* Related fields responsive styles */
  .related-field-name {
    padding-left: 20px;
    font-size: 12px;
  }

  .related-field-value {
    padding-left: 5px;
    font-size: 12px;
  }

  .related-image {
    max-width: 150px;
    max-height: 100px;
  }

  .flag-value {
    font-size: 11px;
    padding: 3px 8px;
  }

  .comment-value {
    padding: 6px 10px;
    font-size: 12px;
  }
}

/* Manager Signatures section styles */
.manager-signatures-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.manager-signatures-section h3 {
  margin: 0 0 20px 0;
  color: #1e40af;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.manager-signatures-section h3::before {
  content: "👔";
  font-size: 1.2rem;
}

.manager-signatures-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.manager-signature-item {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.manager-signature-header {
  margin-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.manager-signature-header h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.signature-timestamp {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

.manager-signature-display {
  text-align: center;
}

.manager-signature-image {
  max-width: 100%;
  max-height: 120px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

/* Print styles for manager signatures */
@media print {
  .manager-signatures-section {
    page-break-inside: avoid;
    margin-top: 20px;
  }

  .manager-signatures-grid {
    grid-template-columns: 1fr 1fr;
  }

  .manager-signature-item {
    break-inside: avoid;
  }
}

/* Mobile responsive styles for manager signatures */
@media (max-width: 767px) {
  .manager-signatures-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .manager-signature-item {
    padding: 12px;
  }

  .manager-signature-header h4 {
    font-size: 1rem;
  }

  .signature-timestamp {
    font-size: 0.8rem;
  }

  .manager-signature-image {
    max-height: 100px;
  }
}
