import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { CoreDataService } from '../../core-data.service';
import { FormJson, FormSubmission } from '../Model/model';
import { ShareService } from '../../SharedData/share-services.service';
import { ActivatedRoute } from '@angular/router';
import { forkJoin, of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Chart, ChartConfiguration, ChartData, ChartType, registerables } from 'chart.js';
import { BaseChartDirective } from 'ng2-charts';
import 'chartjs-adapter-date-fns';

// Register all Chart.js components
Chart.register(...registerables);

@Component({
  selector: 'app-forms',
  templateUrl: './../Template/Forms.component.html',
  styleUrl: './../Style/forms-component.css'
})
export class FormsComponent implements OnInit, AfterViewInit {
  formsList: FormJson[] = [];
  selectedFormId: string | null = null;
  selectedForm: FormJson | null = null;
  isSidebarCollapsed: boolean = false;
  groupedSubmissions: { [date: string]: any[] } = {}; // Group submissions by date
  private triggerSub!: Subscription;

  // Track the current view state of the sidebar
  sidebarView: 'forms' | 'types' | 'submissions' | 'dashboard' = 'forms';

  // Store form submissions
  formSubmissions: FormSubmission[] = [];
  selectedSubmissionId: string | null = null;

  // Store the current form for which types are being shown
  currentFormForTypes: FormJson | null = null;

  // Dashboard data
  showDashboard: boolean = false;
  totalForms: number = 0;
  totalSubmissions: number = 0;
  totalLocations: number = 0;
  totalUsers: number = 0;
  formStats: { formName: string, formId: string, submissionCount: number, lastSubmitted: Date | null }[] = [];
  // Dashboard cards data
  dashboardCards: { title: string, value: number, icon: string }[] = [];

  // Time-based chart data
  selectedFormForChart: any = null;
  startDate: Date = new Date(new Date().setMonth(new Date().getMonth() - 1)); // Default to 1 month ago
  endDate: Date = new Date(); // Default to today
  submissionsByDate: { date: Date, count: number }[] = [];
  formSubmissionDetails: any[] = [];
  submissionsInDateRange: number = 0;

  // Time chart configuration
  @ViewChild('timeChart') timeChart: BaseChartDirective | undefined;

  public timeChartData: ChartData<'line'> = {
    labels: [],
    datasets: [
      {
        data: [],
        label: 'Submissions',
        backgroundColor: 'rgba(33, 150, 243, 0.2)',
        borderColor: 'rgba(33, 150, 243, 1)',
        pointBackgroundColor: 'rgba(33, 150, 243, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(33, 150, 243, 1)',
        fill: 'origin',
        tension: 0.4
      }
    ]
  };

  public timeChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'day',
          displayFormats: {
            day: 'MMM d'
          },
          tooltipFormat: 'MMM d, yyyy'
        },
        title: {
          display: true,
          text: 'Date',
          font: {
            size: 14,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0,0,0,0.05)',
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Submissions',
          font: {
            size: 14,
            weight: 'bold'
          }
        },
        grid: {
          color: 'rgba(0,0,0,0.05)',
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    }
  };

  public timeChartType: ChartType = 'bar';

  // Mock form types for demonstration
  formTypes: string[] = [
    'New',
    // 'In Progress',
    // 'Duplicate',
    // 'Revision',
    'Previously Submitted'
  ];

  constructor(
    private coreDataService: CoreDataService,
    public shareService: ShareService,
    private route: ActivatedRoute
  ) {}

  ngAfterViewInit() {
    // Initialize chart if needed
    if (this.timeChart && this.submissionsByDate.length > 0) {
      this.updateTimeChart();
    }
  }



  ngOnInit() {
    this.sidebarView = 'forms';
    this.formsList = this.shareService.FromList;

    // Subscribe to sidebar view changes
    this.shareService.sidebarViewChange$.subscribe(view => {
      this.sidebarView = view;
    });

    this.shareService.unloadDashBoard$.subscribe(() => {
      this.backToForms();
    });
    this.shareService.showDashBoard$.subscribe(()=>{
      this.showDashboardView();
    })



    // Check for URL parameters
    // this.route.queryParams.subscribe(params => {
    //   // Check for encoded data parameter first
    //   const encodedData = params['data'];
    //   if (encodedData) {
    //     try {
    //       // Decode the Base64 encoded data
    //       const jsonData = decodeURIComponent(atob(encodedData));
    //       const linkData = JSON.parse(jsonData);

    //       // Extract formId and submissionId from the decoded data
    //       const formId = linkData.formId;
    //       const submissionId = linkData.submissionId;

    //       // console.log('Decoded shared link data:', { formId, submissionId });

    //       if (formId && submissionId) {
    //         // Set the IDs and load the form
    //         this.selectedFormId = formId;
    //         this.selectedSubmissionId = submissionId;
    //         // Switch to submissions view
    //         this.shareService.showLocationPopup = false;

    //         // Load form submissions and automatically open in HTML view
    //         this.loadFormSubmissions(formId, true);
    //         this.shareService.changeSidebarView('submissions');
    //       } else {
    //         throw new Error('Missing required IDs in encoded data');
    //       }
    //     } catch (error) {
    //       console.error('Error decoding shared link:', error);
    //       this.shareService.showError('Invalid or corrupted shared link.');
    //     }
    //   } else {
    //     // Fall back to direct parameters for backward compatibility
    //     const formId = params['formId'];
    //     const submissionId = params['submissionId'];

    //     if (formId && submissionId) {
    //       // If both parameters are present, set them directly
    //       this.selectedFormId = formId;
    //       this.selectedSubmissionId = submissionId;
    //       // Switch to submissions view
    //       this.shareService.showLocationPopup = false;
    //       this.loadFormSubmissions(formId, true); // Open in HTML view mode
    //       this.shareService.changeSidebarView('submissions');
    //     } else if (submissionId) {
    //       // If only submissionId is present, load the form ID from the submission data
    //       this.coreDataService.getFormDatabyid(submissionId).subscribe({
    //         next: (data: any) => {
    //           if (data && data.formTemplateId) {
    //             this.selectedFormId = data.formTemplateId;
    //             this.selectedSubmissionId = submissionId;
    //             console.log('Form ID loaded from submission data:', data);
    //             this.shareService.selectedLocation = data.auditHistory.location;
    //             // Switch to submissions view
    //             this.sidebarView = 'submissions';

    //             // Load the form and open in HTML view mode
    //             setTimeout(() => {
    //               this.loadFormSubmissions(data.formTemplateId, true);
    //             }, 100);
    //           }
    //         },
    //         error: (err) => {
    //           console.error('Error loading shared submission:', err);
    //           this.shareService.showError('Could not load the shared form.');
    //         }
    //       });
    //     }
    //   }
    // });
  }

  // loadForms() {
  //   this.coreDataService.GetFORMbyLoc(this.shareService.selectedLocation).subscribe({
  //     next: (data) => {
  //       this.formsList = data;
  //       this.shareService.FromList;
  //     },
  //     error: (err) => {
  //       console.error('Error loading forms:', err);
  //     }
  //   });
  // }

  selectForm(form: any) {
    if (form && form.id) {
      // Switch to the types view and store the current form
      this.sidebarView = 'types';
      this.currentFormForTypes = form;
      this.selectedForm = form;
      this.loadFormSubmissions(form.id);

    } else {
      console.error('Invalid form object or missing id:', form);
    }
  }

  selectFormType(formType: string) {
    // Set the selected form ID and load the form
    if (this.currentFormForTypes && this.currentFormForTypes.id) {
      if (formType === 'Previously Submitted') {
        // Switch to submissions view immediately to show loading state
        this.sidebarView = 'submissions';
        // this.loadFormSubmissions(this.currentFormForTypes.id);
      } else if (formType === 'New') {
        // For new form, we need to reset everything
        this.selectedSubmissionId = null;
        setTimeout(() => {
          if (this.selectedFormId === this.currentFormForTypes?.id) {
            this.selectedFormId = null;
            setTimeout(() => {
              this.selectedFormId = this.currentFormForTypes?.id || null;
              console.log('New form selected with ID:', this.selectedFormId);
            }, 50);
          } else {
            this.selectedFormId = this.currentFormForTypes?.id || null;
          }
        }, 50);
      }
    }
  }

  loadFormSubmissions(formId: string, openInHtmlView: boolean = false) {
    // Clear previous submissions before loading new ones
    this.formSubmissions = [];

    this.coreDataService.getFormDatabyTemplateId(formId).subscribe({
      next: (data: any) => {
        if (data && Array.isArray(data) && data.length > 0) {
          this.formSubmissions = data;

          // console.log('Form submissions loaded:', this.formSubmissions.length, 'submissions found');

          // If openInHtmlView is true and we have a selected submission ID, find and open that submission
          if (openInHtmlView && this.selectedSubmissionId) {
            const selectedSubmission = data.find((submission: FormSubmission) =>
              submission.id === this.selectedSubmissionId);

            if (selectedSubmission) {
              // Open the submission in HTML view mode
              this.selectSubmission(selectedSubmission, true);
            }
          }
        } else {
          // Handle empty or invalid response
          this.formSubmissions = [];
          console.log('No form submissions found for form ID:', formId);
        }
      },
      error: (err) => {
        console.error('Error loading form submissions:', err);
        // Keep the empty array to show the "No data submitted" message
        this.formSubmissions = [];
      }
    });
  }

  selectSubmission(submission: FormSubmission, openInHtmlView: boolean = false) {
    if (submission && submission.id) {
      if (submission.formTemplateId) {
        this.selectedFormId = submission.formTemplateId;
      }

      setTimeout(() => {
        this.selectedSubmissionId = submission.id || null;

        // If openInHtmlView is true, automatically open the form in HTML view mode
        if (openInHtmlView) {
          // Wait for the form to load completely
          setTimeout(() => {
            // Use the ShareService to trigger full window mode
            console.log('Requested to open form in HTML view mode');
          }, 500);
        }
      }, 100);
    }
  }

  backToForms() {
    // Go back to the forms list view
    this.sidebarView = 'forms';
    this.selectedSubmissionId = null;
    this.selectedFormId = null;
  }

  backToFormTypes() {
    // Go back to the form types view
    this.sidebarView = 'types';
    this.selectedSubmissionId = null;
    this.selectedFormId = null;
  }

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  // Dashboard methods
  showDashboardView() {

    this.sidebarView = 'dashboard';
    this.showDashboard = true;
    this.isSidebarCollapsed = true;

    // Initialize dashboard cards
    this.dashboardCards = [
      { title: 'Total Forms', value: 0, icon: 'fa-file-alt' },
      { title: 'Total Submissions', value: 0, icon: 'fa-paper-plane' },
      { title: 'Locations', value: 0, icon: 'fa-map-marker-alt' },
      { title: 'Users', value: 0, icon: 'fa-users' }
    ];

    this.loadDashboardData();
  }

  selectFormForChart(formStat: any) {
    this.selectedFormForChart = formStat;

    // Load submission data for the selected form
    this.coreDataService.getFormDatabyTemplateId(formStat.formId).subscribe({
      next: (submissions: any) => {
        // Update the submission count for this form
        formStat.submissionCount = submissions ? submissions.length : 0;

        let grouped: { [date: string]: any[] } = {};

        submissions.forEach((sub: FormSubmission) => {
          const created = new Date(sub.auditHistory.createdDate!);
          // Use the new extractDateString method to preserve the actual date
          const dateKey = this.extractDateString(sub.auditHistory.createdDate!);
          const time = created.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

          const hasFollowUp = Object.entries(sub.formData).some(
          ([key, value]) => key.endsWith('_flag') && value);

          const item = {
            time,
            createdBy: sub.auditHistory?.createdBy || 'Dev One',
            location: sub.auditHistory.location || 'Location',
            formId: sub.formTemplateId,
            submissionId: sub.id,
            original: sub,
            hasFollowUp // Add this line for highlighting
          };

          if (!grouped[dateKey]) {
            grouped[dateKey] = [];
          }
          grouped[dateKey].push(item);
        });

        this.groupedSubmissions = grouped;

        // Find the most recent submission date - preserve the actual date from backend
        let lastSubmitted: Date | null = null;
        if (submissions && submissions.length > 0) {
          submissions.forEach((sub: any) => {
            if (sub.auditHistory && sub.auditHistory.createdDate) {
              // Use normalizeDate to preserve the actual date without timezone issues
              const subDate = this.normalizeDate(sub.auditHistory.createdDate);
              if (!lastSubmitted || subDate > lastSubmitted) {
                lastSubmitted = subDate;
              }
            }
          });
        }
        formStat.lastSubmitted = lastSubmitted;

        // Load submission data for the chart
        this.loadFormSubmissionsByDate(formStat.formId, submissions);

      },
      error: (err) => {
        console.error('Error loading form submissions:', err);

      }
    });
  }

  sortByDate = (a: any, b: any) => {
    // Since we're now using ISO date strings (YYYY-MM-DD), we can compare them directly
    // or convert to Date objects for proper comparison
    return new Date(b.key).getTime() - new Date(a.key).getTime(); // Latest first
  };

  loadFormSubmissionsByDate(formId: string, submissions?: any[]) {
    // console.log('Loading form submissions for chart with ID:', formId);
    // Clear previous data
    this.formSubmissionDetails = [];
    this.submissionsByDate = [];

    // If submissions are provided, use them directly
    if (submissions && Array.isArray(submissions)) {

      this.formSubmissionDetails = submissions;
      this.processSubmissionsForChart(submissions);
    } else {
      // Otherwise, fetch them from the API
      this.coreDataService.getFormDatabyTemplateId(formId).subscribe({
        next: (submissions: any) => {
          if (submissions && Array.isArray(submissions)) {

            this.formSubmissionDetails = submissions;
            this.processSubmissionsForChart(submissions);
          } else {
            console.warn('API did not return an array of submissions');
            this.formSubmissionDetails = [];
            this.submissionsByDate = [];
            this.submissionsInDateRange = 0;
            this.updateTimeChart();
          }
        },
        error: (err) => {
          console.error('Error loading form submissions for chart:', err);
          this.formSubmissionDetails = [];
          this.submissionsByDate = [];
          this.submissionsInDateRange = 0;
          this.updateTimeChart();
        }
      });
    }
  }

  /**
   * Normalizes a date to the start of the day (00:00:00) without timezone conversion
   * This preserves the actual date from the backend
   * @param date The date to normalize
   * @returns A new Date object set to 00:00:00 of the input date in local timezone
   */
  normalizeDate(date: Date | string): Date {
    let newDate: Date;

    if (typeof date === 'string') {
      // If it's a string, parse it carefully to avoid timezone issues
      if (date.includes('T')) {
        // If it has time component, use as is
        newDate = new Date(date);
      } else {
        // If it's just a date string (YYYY-MM-DD), treat it as local date
        const parts = date.split('-');
        if (parts.length === 3) {
          // Create date in local timezone to avoid UTC conversion
          newDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          newDate = new Date(date);
        }
      }
    } else {
      newDate = new Date(date);
    }

    // Set to start of day in local timezone
    newDate.setHours(0, 0, 0, 0);
    return newDate;
  }

  /**
   * Extracts date string from a Date object or date string without timezone conversion
   * @param date The date to extract from
   * @returns Date string in YYYY-MM-DD format
   */
  extractDateString(date: Date | string): string {
    if (typeof date === 'string') {
      // If it's already a string, check if it's in the right format
      if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return date; // Already in YYYY-MM-DD format
      }
      // If it has time component, extract just the date part
      if (date.includes('T')) {
        return date.split('T')[0];
      }
    }

    // For Date objects, use local date components to avoid timezone issues
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  processSubmissionsForChart(submissions: any[]) {

    // Normalize the start and end dates once for comparison
    const normalizedStartDate = this.normalizeDate(this.startDate);
    const normalizedEndDate = this.normalizeDate(this.endDate);

    // Group submissions by date
    const submissionsByDate = new Map<string, number>();
    let validSubmissionsCount = 0;
    this.submissionsInDateRange = 0;

    submissions.forEach((submission: any, index: number) => {
      if (submission.auditHistory && submission.auditHistory.createdDate) {
        // Extract the date string to preserve the actual date from backend
        const dateKey = this.extractDateString(submission.auditHistory.createdDate);
        const submissionDate = this.normalizeDate(submission.auditHistory.createdDate);

        // Debug logging for first few submissions
        // if (index < 3) {
        //   console.log(`Chart submission ${index}:`, {
        //     originalDate: submission.auditHistory.createdDate,
        //     extractedDateKey: dateKey,
        //     normalizedDate: submissionDate.toISOString(),
        //     inRange: submissionDate >= normalizedStartDate && submissionDate <= normalizedEndDate
        //   });
        // }

        // Check if the submission date is within the selected date range using normalized dates
        if (submissionDate >= normalizedStartDate && submissionDate <= normalizedEndDate) {
          validSubmissionsCount++;

          // Increment count for this date using the extracted date key
          const currentCount = submissionsByDate.get(dateKey) || 0;
          submissionsByDate.set(dateKey, currentCount +1);
        }
      }
    });

    // Convert map to array and sort by date
    this.submissionsByDate = Array.from(submissionsByDate.entries())
      .map(([dateStr, count]) => ({
        date: this.normalizeDate(dateStr), // Use normalizeDate to create proper Date object
        count
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());

    // Calculate total submissions in date range
    this.submissionsInDateRange = this.submissionsByDate.reduce((total, item) => total + item.count, 0);

    // Update the time chart
    this.updateTimeChart();
  }

  updateTimeChart() {
    // Prepare labels and data for the chart
    const labels = this.submissionsByDate.map(item => item.date);
    // console.log('Labels:', labels);
    const data = this.submissionsByDate.map(item => item.count);

    // Create a new dataset to ensure proper rendering
    const newDataset = {
      data: data,
      label: `${this.selectedFormForChart.formName} Submissions`,
      backgroundColor: 'rgba(33, 150, 243, 0.2)',
      borderColor: 'rgba(33, 150, 243, 1)',
      pointBackgroundColor: 'rgba(33, 150, 243, 1)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgba(33, 150, 243, 1)',
      fill: 'origin',
      tension: 0.4
    };

    // Update chart data with a new object to trigger change detection
    this.timeChartData = {
      labels: labels,
      datasets: [newDataset]
    };

    // Force chart update
    setTimeout(() => {
      if (this.timeChart && this.timeChart.chart) {
        this.timeChart.chart.update();
      }
    }, 100);
  }

  updateDateRange() {
    // Log the selected date range (before normalization)
    // console.log("Selected Date Range:",
    //   this.startDate instanceof Date ? this.startDate.toISOString() : this.startDate,
    //   "to",
    //   this.endDate instanceof Date ? this.endDate.toISOString() : this.endDate);

    // Ensure dates are properly set as Date objects
    if (!(this.startDate instanceof Date)) {
      this.startDate = new Date(this.startDate);
    }

    if (!(this.endDate instanceof Date)) {
      this.endDate = new Date(this.endDate);
    }

    if (this.selectedFormForChart && this.formSubmissionDetails.length > 0) {
      // Reuse the already loaded submissions
      console.log('Updating date range with existing submissions');
      this.processSubmissionsForChart([...this.formSubmissionDetails]);
       // Refresh submissions list with new date range
      this.groupedSubmissions = this.filterAndGroupSubmissions(
        this.formSubmissionDetails,
        this.normalizeDate(this.startDate),
        this.normalizeDate(this.endDate)
      );
    }
  }

  // New helper method for filtering and grouping
filterAndGroupSubmissions(
  submissions: any[],
  startDate: Date,
  endDate: Date
): { [date: string]: any[] } {
  const grouped: { [date: string]: any[] } = {};

  submissions.forEach((sub: FormSubmission) => {
    if (!sub.auditHistory?.createdDate) return;

    const created = new Date(sub.auditHistory.createdDate);
    const normalizedDate = this.normalizeDate(created);

    // Skip submissions outside date range
    if (normalizedDate < startDate || normalizedDate > endDate) return;

    const dateKey = this.extractDateString(created);
    const time = created.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    if (!grouped[dateKey]) grouped[dateKey] = [];

    grouped[dateKey].push({
      time,
      createdBy: sub.auditHistory?.createdBy || 'Dev One',
      location: sub.auditHistory.location || 'Location',
      formId: sub.formTemplateId,
      submissionId: sub.id,
      original: sub,
      hasFollowUp: Object.entries(sub.formData).some(
        ([key, value]) => key.endsWith('_flag') && value
      )
    });
  });

  return grouped;
}



  loadDashboardData() {

    const forms$ = this.coreDataService.getAllforms().pipe(
      catchError((error: any) => {
        console.error('Error loading forms for dashboard:', error);
        return of([]);
      })
    );

    const submissions$ = this.coreDataService.getAllFormsData().pipe(
      catchError((error: any) => {
        console.error('Error loading all form submissions:', error);
        return of([]);
      })
    );

    forkJoin([forms$, submissions$]).subscribe(([forms, allSubmissions]: [FormJson[], any[]]) => {
      this.formsList = forms;
      this.totalForms = forms.length;

      // Get unique locations
      const locations = new Set<string>();
      forms.forEach((form: FormJson) => {
        if (form.auditHistory?.location) {
          locations.add(form.auditHistory.location);
        }
      });
      this.totalLocations = locations.size;

      // Get unique users
      const users = new Set<string>();
      forms.forEach((form: FormJson) => {
        if (form.auditHistory?.createdBy) {
          users.add(form.auditHistory.createdBy);
        }
      });
      this.totalUsers = users.size > 0 ? users.size : 1;

      // Prepare form stats
      this.formStats = forms.map((form: FormJson) => ({
        formName: form.auditHistory?.formName || 'Unnamed Form',
        formId: form.id || '',
        submissionCount: 0,
        lastSubmitted: null
      }));

      // Total submissions
      this.totalSubmissions = allSubmissions.length;

      // Dashboard card values
      this.dashboardCards[0].value = this.totalForms;
      this.dashboardCards[1].value = this.totalSubmissions;
      this.dashboardCards[2].value = this.totalLocations;
      this.dashboardCards[3].value = this.totalUsers;

      // Select the first form for the time chart
      if (this.formStats.length > 0 && !this.selectedFormForChart) {
        this.selectFormForChart(this.formStats[0]);
      }

    });
  }

  formViewLink(formId: string, submissionId: string){

     const linkData = {
          formId: formId,
          submissionId: submissionId,
          timestamp: new Date().getTime(),
          directHtmlView: true // Flag to indicate direct HTML view
        };

    // Convert to JSON and encode with Base64
        const jsonData = JSON.stringify(linkData);
        const encodedData = btoa(encodeURIComponent(jsonData));

        const baseUrl = window.location.origin;
        const shareableLink = `${baseUrl}/form-preview?data=${encodedData}`;

        window.open(shareableLink, "_blank");
  }

}
