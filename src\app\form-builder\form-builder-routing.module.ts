import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FormBuilderComponent } from './Component/form-builder.component';
import { FormListComponent } from './Component/form-list.component';
import { FormConfigComponent } from './Component/form-config.component';

const routes: Routes = [
  {
    path: 'builder',
    component: FormBuilderComponent
  },
  {
    path:'List',
    component: FormListComponent
  },
  {
    path: 'config',
    component: FormConfigComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FormBuilderRoutingModule { }
