import { HomeComponent } from './home.component';
import { Component, Input, OnInit, ElementRef, ViewChild } from '@angular/core';
import { ShareService } from '../../SharedData/share-services.service';
// @ts-ignore
import html2pdf from 'html2pdf.js';
import { CoreDataService } from '../../core-data.service';

@Component({
  selector: 'app-form-html-view',
  templateUrl: '../Template/form-html-view.component.html',
  styleUrls: ['../Style/form-html-view.component.css']
})
export class FormHtmlViewComponent implements OnInit {
  @ViewChild('formContent') formContent!: ElementRef;
  @ViewChild('homeView') homeView!: HomeComponent;

  @Input() Data: any; // This will now be formGroup.value
  @Input() formJson: any; // Optional: Form JSON structure for getting field labels
  @Input() loadedImages: { key: string, data: <PERSON>rray<PERSON>uffer }[] = [];
  @Input() signatureImage: string | null = null;
  @Input() guidanceImages: any = {};
  @Input() formComponents: any[] = []; // To access the components with imageDisplayUrl
  @Input() managerSignatures: Array<{id: string, name: string, signature: string, timestamp: string}> = [];

  formName: string = 'Form';
  userName: string = 'User';
  formFields: {
    key: string,
    label: string,
    value: any,
    type: string,
    component?: any,
    sectionTitle?: string,
    relatedFields?: {
      key: string,
      label: string,
      value: any,
      type: string,
      suffix: string
    }[]
  }[] = [];

  // Organized sections for display
  formSections: {
    title: string,
    fields: {
      key: string,
      label: string,
      value: any,
      type: string,
      component?: any,
      relatedFields?: {
        key: string,
        label: string,
        value: any,
        type: string,
        suffix: string
      }[]
    }[]
  }[] = [];

  // Image display options
  imageMaxWidth: number = 300;
  imageMaxHeight: number = 200;

  constructor(
    public shareService: ShareService,
    private coreDataService: CoreDataService
  ) {}

  ngOnInit(): void {
    console.log('Form HTML View initializing...');

    // Clear local image storage
    this.serverLoadedImages = {};
    this.loadingImages.clear();
    this.imageUrls = {}; // Clear the image URLs cache
    this.imagesToLoad = []; // Clear the images to load array
    this.loadedImageKeys.clear(); // Clear the loaded image keys

    this.initializeFormData();

  }
  // This property is now defined below with the other image-related properties

  initializeFormData(): void {
    if (!this.Data) {
      console.error('No form data provided');
      return;
    }
    // this.homeView.loadImagesForComponents();

    // Set form name and user name from formJson if available
    if (this.formJson && this.formJson.auditHistory) {
      this.formName = this.formJson.auditHistory.formName || 'Form';
      this.userName = this.formJson.auditHistory.userName || 'User';
    }

    // Process form fields
    this.processFormFields();
  }

 async processFormFields(): Promise<void> {
    this.formFields = [];
    this.formSections = [];

    // Skip these fields - only skip internal fields, not user data
    const skipFields = ['auditHistory', 'component', 'id', 'formTemplateId'];

    // Create a map to track which section each field belongs to
    const fieldToSectionMap: { [key: string]: string } = {};

    // If formJson is available, map fields to their sections
    if (this.formJson && this.formJson.component) {
      for (const section of this.formJson.component) {
        if (section.elements) {
          for (const element of section.elements) {
            if (element.attributes && element.attributes.field_name) {
              fieldToSectionMap[element.attributes.field_name] = section.title;
            }
          }
        }
      }
    }

    // First pass: collect all parent fields and their related fields
    const parentFields: { [key: string]: any } = {};
    const relatedFields: { [parentKey: string]: any[] } = {};

    // Process form data to identify parent and related fields
    for (const [key, value] of Object.entries(this.Data)) {
      // Skip internal fields
      if (skipFields.includes(key)) {
        continue;
      }

      // Check if this is a related field (_flag, _comment, _image)
      if (key.includes('_flag') || key.includes('_comment') || key.includes('_image')) {
        // Only process if the field has a value
        if (value && value !== '' && value !== null && value !== undefined) {
          // Extract parent field name
          let parentKey = key;
          let suffix = '';

          if (key.includes('_flag')) {
            parentKey = key.replace('_flag', '');
            suffix = '_flag';
          } else if (key.includes('_comment')) {
            parentKey = key.replace('_comment', '');
            suffix = '_comment';
          } else if (key.includes('_image')) {
            parentKey = key.replace('_image', '');
            suffix = '_image';
          }

          // Initialize related fields array for parent if not exists
          if (!relatedFields[parentKey]) {
            relatedFields[parentKey] = [];
          }

          // Determine field type for related field
          let relatedFieldType = 'text';
          let processedValue = value;

          if (suffix === '_flag') {
            relatedFieldType = 'flag';
          } else if (suffix === '_comment') {
            relatedFieldType = 'comment';
          } else if (suffix === '_image') {
            relatedFieldType = 'image';
            // Handle image loading for related image fields
            if (typeof value === 'string' &&
                (value.startsWith('C:') ||
                 value.startsWith('D:') ||
                 value.includes('.jpg') ||
                 value.includes('.jpeg') ||
                 value.includes('.png') ||
                 value.includes('.gif'))) {

              const imagePath = value as string;
              if (!this.loadedImageKeys.has(key)) {
                this.imagesToLoad.push({ key, path: imagePath });
                this.loadedImageKeys.add(key);
              }
            }
          }

          // Add to related fields
          relatedFields[parentKey].push({
            key,
            label: this.getFieldLabel(key),
            value: processedValue,
            type: relatedFieldType,
            suffix
          });
        }
        continue; // Skip processing this as a main field
      }

      // This is a parent field, store it
      parentFields[key] = { key, value, sectionTitle: fieldToSectionMap[key] || 'Information' };
    }

    // Second pass: process parent fields and attach their related fields
    for (const [key, parentData] of Object.entries(parentFields)) {
      const { value } = parentData;

      // Determine field type and format value appropriately
      let fieldType = 'text';
      let processedValue = value;

      // Check if this is a signature field
      if (key.toLowerCase().includes('signature') &&
          typeof value === 'string' &&
          value.startsWith('data:image')) {
        fieldType = 'signature';
        // No need to process the value, keep the base64 image data
      }
      // Check if this is a guidance image field
      else if (key.toLowerCase().includes('guidance') ||
               key.toLowerCase().includes('guide') ||
               key.toLowerCase().includes('Guidance Image') ||
               (this.guidanceImages && this.guidanceImages[key])) {
        fieldType = 'guidance';

        // Get the image path from the value or from guidanceImages
        const imagePath = typeof value === 'string' ? value :
                         (this.guidanceImages ? this.guidanceImages[key] : null);

        // Check if the image is already loaded in the form components
        let imageAlreadyLoaded = false;
        if (this.formJson && this.formJson.component) {
          for (const section of this.formJson.component) {
            for (const component of section.elements) {
              if (component.attributes &&
                  (component.attributes.field_name === key ||
                   component.attributes.label === key) &&
                  component.attributes.imageDisplayUrl) {
                imageAlreadyLoaded = true;
                this.imageUrls[key] = component.attributes.imageDisplayUrl; // Cache the URL
                this.loadedImageKeys.add(key); // Mark as loaded
                break;
              }
            }
            if (imageAlreadyLoaded) break;
          }
        }

        // Only load if not already loaded or loading
        if (imagePath && !this.loadedImageKeys.has(key) && !imageAlreadyLoaded) {
          // Add to the list of images to load
          this.imagesToLoad.push({ key, path: imagePath });
          this.loadedImageKeys.add(key); // Mark as loaded
        }
      }
      // Check if this is an image field with a loaded image
      else if (this.loadedImages.find(img => img.key === key)) {
        fieldType = 'image';
      }
      // Check if this is an image field with a file path
      else if (typeof value === 'string' &&
              (value.startsWith('C:') ||
               value.startsWith('D:') ||
               value.includes('.jpg') ||
               value.includes('.jpeg') ||
               value.includes('.png') ||
               value.includes('.gif'))) {
        fieldType = 'image';

        // Get the image directly from the path in the value field
        const imagePath = value as string;

        // Store the image path for batch loading later
        if (!this.loadedImageKeys.has(key)) {
          // Add to a list of images to load
          this.imagesToLoad.push({ key, path: imagePath });
          this.loadedImageKeys.add(key);
          // Optionally, you can load the image immediately here instead of batching
          // this.loadImageFromServer(key, imagePath);
        }
      }
      // Check if this is a date field
      else if (typeof value === 'string' && this.isDateString(value)) {
        fieldType = 'date';
        try {
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            processedValue = date.toLocaleDateString();
          }
        } catch (e) {
          // Keep original value if date parsing fails
        }
      }
      // Check if this is a multiselect field (array value)
      else if (Array.isArray(value)) {
        fieldType = 'multiselect';
        processedValue = value.join(', ');
      }
      // Check if this is a boolean field
      else if (typeof value === 'boolean') {
        fieldType = 'boolean';
        processedValue = value;
      }
      // Check if this is a map field (JSON string with lat/lng)
      else if (typeof value === 'string' &&
              value.includes('lat') &&
              value.includes('lng')) {
        fieldType = 'map';
        try {
          const coords = JSON.parse(value);
          processedValue = `Latitude: ${coords.lat}, Longitude: ${coords.lng}`;
        } catch (e) {
          // Keep original value if parsing fails
        }
      }
      // Check if this is a JSON object
      else if (typeof value === 'object' && value !== null) {
        fieldType = 'object';
        try {
          processedValue = JSON.stringify(value, null, 2);
        } catch (e) {
          // Keep original value if JSON stringification fails
        }
      }

      // Add to form fields array with related fields
      this.formFields.push({
        key,
        label: this.getFieldLabel(key),
        value: processedValue,
        type: fieldType,
        sectionTitle: parentData.sectionTitle,
        relatedFields: relatedFields[key] || [] // Attach related fields if any
      });
    }

    // Organize fields by section
    this.organizeFieldsBySection();

    // Load all images at once
    this.loadAllImages();
  }

  /**
   * Load all images that were identified during processFormFields
   * This batches the image loading to reduce API calls
   */
  private loadAllImages(): void {
    if (this.imagesToLoad.length === 0) {
      return;
    }


    // Load each image
    for (const imageInfo of this.imagesToLoad) {
      // Only load if not already loaded, loading, or marked as loaded
      if (!this.serverLoadedImages[imageInfo.key] &&
          !this.loadingImages.has(imageInfo.key) &&
          !this.loadedImageKeys.has(imageInfo.key)) {
        this.loadImageFromServer(imageInfo.key, imageInfo.path);
      }
    }

    // Clear the array after loading
    this.imagesToLoad = [];
  }

  // Organize fields into sections for display
  organizeFieldsBySection(): void {
    // Clear existing sections
    this.formSections = [];

    // Group fields by section title
    const sectionMap: { [sectionTitle: string]: any[] } = {};

    // Group fields by their section
    for (const field of this.formFields) {
      // Get section title, but don't use 'Information' as default
      let sectionTitle = field.sectionTitle;

      // Skip fields without a section title
      if (!sectionTitle || sectionTitle === 'Information') {
        // If the field has no section or is in the default Information section,
        // check if we can determine a better section from the form structure
        if (this.formJson && this.formJson.component && this.formJson.component.length > 0) {
          // If there's only one section in the form, use that
          if (this.formJson.component.length === 1) {
            sectionTitle = this.formJson.component[0].title;
          }
        }

        // If we still don't have a section title, skip this field
        if (!sectionTitle || sectionTitle === 'Information') {
          continue;
        }
      }

      if (!sectionMap[sectionTitle]) {
        sectionMap[sectionTitle] = [];
      }

      sectionMap[sectionTitle].push({
        key: field.key,
        label: field.label,
        value: field.value,
        type: field.type,
        component: field.component,
        relatedFields: field.relatedFields || [] // Include related fields
      });
    }

    // Convert the map to an array of sections
    for (const [title, fields] of Object.entries(sectionMap)) {
      // Only add sections that have fields
      if (fields.length > 0) {
        this.formSections.push({
          title,
          fields
        });
      }
    }
  }

  // Helper method to check if a string is a valid date
  isDateString(str: string): boolean {
    // Check if the string is a valid date format
    const date = new Date(str);
    return !isNaN(date.getTime()) &&
           // Additional check to avoid treating numbers as dates
           (str.includes('-') || str.includes('/') || str.includes('.'));
  }

  // Array to store server-loaded images
  serverLoadedImages: { [key: string]: string } = {};

  // Set to track images that are currently being loaded
  private loadingImages: Set<string> = new Set();

  // Set to track which image keys have already been processed
  private loadedImageKeys: Set<string> = new Set();

  // Array to store images that need to be loaded
  private imagesToLoad: { key: string, path: string }[] = [];

  // Load image from server directly using the CoreDataService
  loadImageFromServer(key: string, path: string): void {
    // Check if this image is already being loaded
    if (this.loadingImages.has(key)) {
      console.log(`Form-HTML-View: Image ${key} is already being loaded, skipping duplicate request`);
      return;
    }

    // Mark this image as being loaded
    this.loadingImages.add(key);

    // Call the API directly to get the image
    this.coreDataService.getImage(path).subscribe({
      next: (blob) => {
        if (blob) {
          console.log(`Form-HTML-View: Image ${key} loaded successfully`);

          // Convert blob to data URL
          const reader = new FileReader();
          reader.onloadend = () => {
            const dataUrl = reader.result as string;
            this.serverLoadedImages[key] = dataUrl;
            this.imageUrls[key] = dataUrl; // Update the image URLs cache
            this.loadedImageKeys.add(key); // Mark as loaded

            // Remove from loading set
            this.loadingImages.delete(key);
          };
          reader.readAsDataURL(blob);
        } else {
          console.error(`Form-HTML-View: Failed to load image ${key}`);
          // Set a placeholder or error image
          const placeholder = 'assets/Images/pdf.png';
          this.serverLoadedImages[key] = placeholder;
          this.imageUrls[key] = placeholder; // Update the image URLs cache
          this.loadedImageKeys.add(key); // Mark as loaded even if there's an error

          // Remove from loading set
          this.loadingImages.delete(key);
        }
      },
      error: (error) => {
        console.error(`Form-HTML-View: Error loading image for ${key}:`, error);
        // Set a placeholder or error image on error
        const placeholder = 'assets/Images/pdf.png';
        this.serverLoadedImages[key] = placeholder;
        this.imageUrls[key] = placeholder; // Update the image URLs cache
        this.loadedImageKeys.add(key); // Mark as loaded even if there's an error

        // Remove from loading set
        this.loadingImages.delete(key);
      }
    });
  }

  getFieldLabel(key: string): string {
    // Try to find the field in the form components if formJson is available
    if (this.formJson && this.formJson.component) {
      for (const section of this.formJson.component) {
        if (section.elements) {
          for (const element of section.elements) {
            if (element.attributes && element.attributes.field_name === key) {
              return element.attributes.label || key;
            }
          }
        }
      }
    }

    // Handle related field labels
    if (key.includes('_flag')) {
      return 'Follow Up';
    } else if (key.includes('_comment')) {
      return 'Comment';
    } else if (key.includes('_image')) {
      return 'Image';
    }

    // If not found, return the key with first letter capitalized and underscores replaced with spaces
    // Also remove _image, _comment, and _flag suffixes for display
    return key
      .replace(/_image|_comment|_flag/g, '') // Remove suffixes
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  // Store image URLs to avoid repeated API calls
  private imageUrls: { [key: string]: string } = {};

  /**
   * Get the image URL for a field
   * This method only returns already loaded/cached images and doesn't trigger new API calls
   */
  getImageUrl(key: string): string | null {
    // First check our local cache of image URLs
    if (this.imageUrls[key]) {
      // URL is already cached, return it without logging to reduce console spam
      return this.imageUrls[key];
    }

    // Check if this image is available in the formComponents (already loaded in home component)
    if (this.formJson && this.formJson.component) {
      // Find the component with matching field name
      for (const section of this.formJson.component) {
        for (const component of section.elements) {
          if (component.attributes &&
              (component.attributes.field_name === key ||
               component.attributes.label === key) &&
              component.attributes.imageDisplayUrl) {

            // Found the image in the form components
            const url = component.attributes.imageDisplayUrl;
            this.imageUrls[key] = url; // Cache the URL
            console.log(`Image URL for ${key} found in formComponents`);
            return url;
          }
        }
      }
    }

    // For server-loaded images in the component
    if (this.serverLoadedImages && this.serverLoadedImages[key]) {
      const url = this.serverLoadedImages[key];
      this.imageUrls[key] = url; // Cache the URL
      console.log(`Image URL for ${key} found in serverLoadedImages`);
      return url;
    }

    // If the key is in loadedImageKeys but not in serverLoadedImages, it's still loading
    if (this.loadedImageKeys.has(key)) {
      console.log(`Image ${key} is marked as loaded but not yet available, still loading...`);
    }

    // For loaded images from input
    const loadedImage = this.loadedImages.find(img => img.key === key);
    if (loadedImage && loadedImage.data) {
      try {
        // Convert ArrayBuffer to base64 string
        const bytes = new Uint8Array(loadedImage.data);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
          binary += String.fromCharCode(bytes[i]);
        }

        // Determine image type based on the first few bytes
        const header = new Uint8Array(loadedImage.data, 0, 4);
        const isPng = header[0] === 0x89 && header[1] === 0x50 && header[2] === 0x4E && header[3] === 0x47;
        const isJpeg = header[0] === 0xFF && header[1] === 0xD8;

        const mimeType = isPng ? 'image/png' : (isJpeg ? 'image/jpeg' : 'image/png');
        const url = `data:${mimeType};base64,${btoa(binary)}`;
        this.imageUrls[key] = url; // Cache the URL
        return url;
      } catch (error) {
        console.error('Error converting image data:', error);
        return null;
      }
    }

    // If we get here, the image isn't available yet
    return ''; // Return a loading image
  }

  // Check if any field is a signature field
  hasSignatureField(): boolean {
    // Check if any field is explicitly a signature field
    if (this.formFields.some(field => field.type === 'signature')) {
      return true;
    }

    // Check if any field in the form data has 'signature' in its name and is a data URL
    if (this.Data) {
      for (const [key, value] of Object.entries(this.Data)) {
        if (key.toLowerCase().includes('signature') &&
            typeof value === 'string' &&
            value.startsWith('data:image')) {
          return true;
        }
      }
    }

    // Check if there's a component with signature type in formJson
    if (this.formJson && this.formJson.component) {
      for (const section of this.formJson.component) {
        if (section.elements) {
          for (const element of section.elements) {
            if (element.type === 'signature' ||
                (element.attributes &&
                 element.attributes.field_name &&
                 element.attributes.field_name.toLowerCase().includes('signature'))) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  isGeneratingPdf: boolean = false;

  generatePdf(shouldReturnBytes: boolean = false): Promise<Uint8Array | void> {
    if (!this.formContent) {
      this.shareService.showError('Form content not available');
      return Promise.reject(new Error('Form content not available'));
    }

    const element = this.formContent.nativeElement;
    const options = {
      margin: [10, 10, 10, 10] as [number, number, number, number],
      filename: `${this.formName}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        logging: true
      },
      jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait' as 'portrait' | 'landscape'
      }
    };

    return new Promise<Uint8Array | void>((resolve, reject) => {
      // Create a worker instance
      const worker = html2pdf().from(element).set(options);

      if (shouldReturnBytes) {
        // If we need to return the bytes, get the PDF as blob
        this.shareService.showInfo('Generating PDF...');
        worker.output('blob')
          .then((blob: Blob) => {
            // Convert blob to Uint8Array
            const reader = new FileReader();
            reader.onloadend = () => {
              const arrayBuffer = reader.result as ArrayBuffer;
              const uint8Array = new Uint8Array(arrayBuffer);
              resolve(uint8Array);
            };
            reader.onerror = () => {
              reject(new Error('Failed to read PDF blob'));
            };
            reader.readAsArrayBuffer(blob);
          })
          .catch((error: any) => {
            console.error('Error generating PDF:', error);
            this.shareService.showError('Error generating PDF. Please try again.');
            reject(error);
          });
      } else {
        // Just save the PDF
        worker.save()
          .then(() => {
            console.log('PDF generated successfully');
            resolve();
          })
          .catch((error: any) => {
            console.error('Error generating PDF:', error);
            this.shareService.showError('Error generating PDF. Please try again.');
            reject(error);
          });
      }
    });
  }
}
