import { Component, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CoreDataService } from '../../core-data.service';
import { ShareService } from '../../SharedData/share-services.service';
import { Form<PERSON>son, FormSubmission } from '../Model/model';
import { firstValueFrom, Subscription } from 'rxjs';
import { OneDriveService, OneDriveFolder } from '../../SharedData/OneDriveService';
import { MsalService } from '@azure/msal-angular';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { FormHtmlViewComponent } from './form-html-view.component';


@Component({
  selector: 'app-shared-form-view',
  template: `
    <div class="shared-form-container">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Loading shared form...</p>
        </div>

      <!-- Error State -->
      <div *ngIf="hasError" class="error-container">
        <div class="error-messages">
          <i class="fas fa-exclamation-triangle"></i>
          <h3>Unable to Load Form</h3>
          <p>{{ errorMessage }}</p>
        </div>
      </div>

      <!-- HTML Form View - Full Window Mode Style -->
      <div *ngIf="!isLoading && !hasError && formData && submissionData" class="full-window-mode">
        <div class="full-window-header">
          <div class="full-window-title">
            {{ formData.auditHistory.formName || 'Shared Form' }}
          </div>
          <div class="header-actions">
            <ul class="Action-btn">
              <li class="action-button" (click)="generatePdf()" title="Download PDF">
                <span class="action-text">Download PDF</span>
              </li>
              <li class="action-button" (click)="generateShareableLink()" title="Copy Link">
                <span class="action-text">Share Form via Link</span>
              </li>
              <li class="action-button" (click)="showOneDrivePopup()" title="Save in One Drive">
                <span class="action-text">Save in One Drive</span>
              </li>
              <li class="action-button" (click)="sendEmailWithOutlookDraft()" title="Send via Email">
                <span class="action-text">Send via Mail</span>
              </li>
            </ul>
          </div>
        </div>
        <app-form-html-view
          #formHtmlView
          [Data]="submissionData.formData"
          [formJson]="formData"
          [loadedImages]="loadedImages"
          [signatureImage]="signatureData"
          [guidanceImages]="guidanceImages"
          [formComponents]="formData.component || []"
          [managerSignatures]="managerSignatures">
        </app-form-html-view>
      </div>

      <!-- OneDrive Popup -->
    <div class="onedrive-popup-overlay" *ngIf="showOneDriveDialog">
      <div class="onedrive-popup">
        <div class="onedrive-popup-header">
          <h3>Save to OneDrive</h3>
          <div class="header-actions">
            <button class="account-btn" (click)="switchAccount()" title="Switch Microsoft Account">
              <i class="fas fa-user-circle"></i> Switch Account
            </button>
            <button class="close-btn" (click)="hideOneDrivePopup()">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <div class="onedrive-popup-content">
          <!-- Loading indicator -->
          <div *ngIf="isLoadingFolders" class="loading-indicator">
            <div class="spinner"></div>
            <p>Loading folders...</p>
          </div>

          <!-- Error message -->
          <div *ngIf="oneDriveError" class="error-messages">
            <i class="fas fa-exclamation-circle"></i> {{ oneDriveError }}
            <button (click)="loadOneDriveFolders()" class="retry-btn">Retry</button>
          </div>

          <!-- Folder list -->
          <div *ngIf="!isLoadingFolders && !oneDriveError" class="folder-list">
            <div class="folder-actions">
              <h4>Select a folder to save to:</h4>
              <button class="new-folder-btn" (click)="showNewFolderInput()">
                <i class="fas fa-folder-plus"></i> New Folder
              </button>
            </div>

            <!-- New folder input -->
            <div *ngIf="showingNewFolderInput" class="new-folder-input">
              <div class="new-folder-location">
                <span>Creating folder in: </span>
                <strong *ngIf="currentFolderId === 'Home'">Home</strong>
                <strong *ngIf="currentFolderId !== 'Home'">
                  {{ getCurrentFolderName() }}
                </strong>
              </div>
              <input type="text" [(ngModel)]="newFolderName" placeholder="Folder name"
                     (keyup.enter)="createNewFolder()">
              <div class="new-folder-buttons">
                <button (click)="createNewFolder()" [disabled]="!newFolderName">Create</button>
                <button (click)="cancelNewFolder()">Cancel</button>
              </div>
            </div>

            <!-- Current path -->
            <div class="folder-path" *ngIf="folderPath.length > 0">
              <span (click)="navigateToRoot()">Home</span>
              <span *ngFor="let pathItem of folderPath; let i = index">
                <i class="fas fa-chevron-right"></i>
                <span (click)="navigateToPathItem(i)">{{ pathItem.name }}</span>
              </span>
            </div>

            <div *ngIf="folders.length === 0" class="no-folders">
              <p>No folders found in this location</p>
            </div>

            <ul class="folder-tree">
              <li *ngFor="let folder of displayedFolders"
                  [class.selected]="selectedFolderId === folder.id"
                  [class.expanded]="folder.isExpanded">
                <div class="folder-item" (click)="selectFolder(folder)">
                  <span class="folder-expand" *ngIf="folder.isLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                  </span>
                  <i class="fas fa-folder"></i>
                  <span class="folder-name">{{ folder.name }}</span>
                  <button class="folder-navigate-btn" (click)="$event.stopPropagation(); navigateToFolder(folder)" title="Open this folder">
                    <i class="fas fa-arrow-right"></i>
                  </button>
                </div>

                <!-- Subfolders -->
                <ul *ngIf="folder.isExpanded && folder.children && folder.children.length > 0" class="subfolder-list">
                  <li *ngFor="let subfolder of folder.children"
                      [class.selected]="selectedFolderId === subfolder.id">
                    <div class="subfolder-item" (click)="selectFolder(subfolder)">
                      <i class="fas fa-folder"></i>
                      <span class="folder-name">{{ subfolder.name }}</span>
                      <button class="folder-navigate-btn" (click)="$event.stopPropagation(); navigateToFolder(subfolder)" title="Open this folder">
                        <i class="fas fa-arrow-right"></i>
                      </button>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>

          <!-- Upload progress -->
          <div *ngIf="isUploading" class="upload-progress">
            <div class="progress-bar">
              <div class="progress" [style.width.%]="uploadProgress"></div>
            </div>
            <p>Uploading file... {{ uploadProgress }}%</p>
          </div>
        </div>

        <div class="onedrive-popup-footer">
          <button class="cancel-btn" (click)="hideOneDrivePopup()">Cancel</button>
          <button class="upload-btn"
                  [disabled]="!selectedFolderId || selectedFolderId === 'Home' || isUploading"
                  (click)="uploadToSelectedFolder()">
            <i class="fas fa-cloud-upload-alt"></i>
            <span *ngIf="selectedFolderId && selectedFolderId !== 'Home'" class="upload-path">Save to "{{ getSelectedFolderName() }}"</span>
            <span *ngIf="!selectedFolderId || selectedFolderId === 'Home'">Select a specific folder first</span>
          </button>
        </div>
      </div>
    </div>




  `,
  styles: [`
    .shared-form-container {
      width: 100%;
      min-height: 100vh;
      background-color: #f5f5f5;
      margin: 0;
      padding: 0;
    }

    .loading-container, .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 50vh;
    }

    .loading-spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #007bff;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-messages {
      text-align: center;
      color: #dc3545;
    }

    .error-messages i {
      font-size: 48px;
      margin-bottom: 20px;
    }

    /* OneDrive Popup Styles
    in global style.css*/
  `]
})
export class SharedFormViewComponent implements OnInit, OnDestroy {
  @ViewChild('formHtmlView') formHtmlView!: FormHtmlViewComponent;
  managerSignatures: Array<{id: string, name: string, signature: string, timestamp: string}> = [];
  isLoading = true;
  hasError = false;
  errorMessage = '';

  formData: FormJson | null = null;
  submissionData: FormSubmission | null = null;
  loadedImages: { key: string; data: ArrayBuffer }[] = [];
  signatureData: string | null = null;
  guidanceImages: { [key: string]: string } = {};
  uploadedImages: { [key: string]: string | ArrayBuffer | null } = {};

  // OneDrive popup properties
  showOneDriveDialog = false;
  isLoadingFolders = false;
  oneDriveError = '';
  isUploading = false;
  uploadProgress = 0;
  pdfFile: File | null = null;
  folders: OneDriveFolder[] = [];
  displayedFolders: OneDriveFolder[] = [];
  selectedFolderId: string = '';
  currentFolderId: string = 'Home';
  folderPath: OneDriveFolder[] = [];
  showingNewFolderInput = false;
  newFolderName = '';

  private subscriptions: Subscription[] = [];
  private oneDriveSubscriptions: Subscription[] = [];

  constructor(
    private route: ActivatedRoute,
    private coreDataService: CoreDataService,
    private shareService: ShareService,
    private oneDriveService: OneDriveService,
    private msalService: MsalService,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      const encodedData = params['data'];
      if (encodedData) {
        this.loadSharedForm(encodedData);
      } else {
        this.showError('Invalid or missing form data in the link.');
      }
    });
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.oneDriveSubscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadSharedForm(encodedData: string) {
    try {
      // Decode the shared link data
      const jsonData = decodeURIComponent(atob(encodedData));
      const linkData = JSON.parse(jsonData);

      const { formId, submissionId, directHtmlView } = linkData;

      if (!formId || !submissionId) {
        throw new Error('Missing required form or submission ID');
      }

      // Load form template first
      this.subscriptions.push(
        this.coreDataService.getFormByID(formId).subscribe({
          next: (formTemplate: FormJson) => {
            this.formData = formTemplate;

            // Then load submission data
            this.subscriptions.push(
              this.coreDataService.getFormDatabyid(submissionId).subscribe({
                next: (submissionData: any) => {
                  this.submissionData = submissionData as FormSubmission;

                  // Load images after both form and submission data are loaded
                  this.loadFormImages().then(() => {
                    this.isLoading = false;
                  });
                },
                error: (err) => {
                  console.error('Error loading submission data:', err);
                  this.showError('Failed to load the submission data.');
                }
              })
            );
          },
          error: (err) => {
            console.error('Error loading form template:', err);
            this.showError('Failed to load the form template.');
          }
        })
      );

    } catch (error) {
      console.error('Error decoding shared form data:', error);
      this.showError('Failed to load the shared form. The link may be invalid or expired.');
    }
  }

  private loadFormImages(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.formData || !this.submissionData) {
        resolve();
        return;
      }

      let pendingImages = 0;
      let completedImages = 0;

      const checkComplete = () => {
        completedImages++;
        if (completedImages >= pendingImages) {
          resolve();
        }
      };

      // Load guidance images and set imageDisplayUrl for components (like HomeComponent)
      for (const section of this.formData.component) {
        for (const component of section.elements) {
          if (component.type === 'image' && component.attributes.image_url) {
            const imageKey = component.attributes.field_name || component.attributes.label || `guidance_${Math.random().toString(36).substring(2, 9)}`;

            pendingImages++;
            this.subscriptions.push(
              this.coreDataService.getImage(component.attributes.image_url).subscribe({
                next: (blob) => {
                  if (blob) {
                    const objectUrl = URL.createObjectURL(blob);
                    // Store in guidanceImages for form-html-view
                    this.guidanceImages[imageKey] = component.attributes.image_url || '';
                    // Set imageDisplayUrl on component for immediate access (like HomeComponent)
                    component.attributes.imageDisplayUrl = objectUrl;
                    console.log(`Shared-Form-View: Guidance image loaded for ${imageKey}`);
                  }
                  checkComplete();
                },
                error: () => {
                  console.warn('Failed to load guidance image:', component.attributes.image_url);
                  // Set placeholder
                  component.attributes.imageDisplayUrl = 'assets/Images/placeholder-image.png';
                  checkComplete();
                }
              })
            );
          }
        }
      }

      // Load uploaded images from submission data (like HomeComponent)
      if (this.submissionData.formData) {
        for (const [key, value] of Object.entries(this.submissionData.formData)) {
          if (typeof value === 'string') {
            // Handle signature data (base64)
            if (key === 'signature' && value.startsWith('data:image')) {
              this.signatureData = value;
              console.log(`Shared-Form-View: Signature data loaded for ${key}`);
            }
            // Handle uploaded image paths (server paths)
            else if (value.startsWith('D:') || value.startsWith('C:') || value.includes('.jpg') || value.includes('.jpeg') || value.includes('.png')) {
              pendingImages++;
              this.subscriptions.push(
                this.coreDataService.getImage(value).subscribe({
                  next: async (blob) => {
                    if (blob) {
                      try {
                        // Convert blob to ArrayBuffer for loadedImages (expected by form-html-view)
                        const arrayBuffer = await blob.arrayBuffer();
                        this.loadedImages.push({ key, data: arrayBuffer });

                        // Also store in uploadedImages for compatibility
                        this.uploadedImages[key] = arrayBuffer;

                        console.log(`Shared-Form-View: Uploaded image loaded for ${key}`);
                      } catch (error) {
                        console.warn(`Failed to convert blob to ArrayBuffer for ${key}:`, error);
                      }
                    }
                    checkComplete();
                  },
                  error: () => {
                    console.warn(`Failed to load uploaded image: ${key} -> ${value}`);
                    checkComplete();
                  }
                })
              );
            }
          }
        }
      }

      // If no images to load, resolve immediately
      if (pendingImages === 0) {
        resolve();
      }
    });
  }

  private showError(message: string) {
    this.hasError = true;
    this.errorMessage = message;
    this.isLoading = false;
  }

  generatePdf() {
    if (!this.formHtmlView) {
      this.shareService.showError('Form view not available for PDF generation.');
      return;
    }
    // Call the generatePdf method directly from the form-html-view component
    this.formHtmlView.generatePdf();
  }

  generateShareableLink() {
    // Copy the current URL as it's already a shareable link
    const currentUrl = window.location.href;
    navigator.clipboard.writeText(currentUrl).then(() => {
      this.shareService.showSuccess('Shareable link copied to clipboard!');
    }).catch(() => {
      this.shareService.showError('Failed to copy link to clipboard.');
    });
  }

  arrayBufferToBase64(buffer: Uint8Array): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Create a new Uint8Array to ensure we're working with a clean copy
        const bytes = new Uint8Array(buffer);
        const blob = new Blob([bytes], { type: 'application/pdf' });
        const reader = new FileReader();
        reader.onload = () => {
          if (reader.result) {
            const base64 = (reader.result as string).split(',')[1];
            resolve(base64);
          } else {
            reject(new Error('Failed to read file data'));
          }
        };
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(blob);
      } catch (error) {
        console.error('Error in arrayBufferToBase64:', error);
        reject(error);
      }
    });
  }

  scopes = ['Mail.ReadWrite', 'Mail.Send', 'Files.ReadWrite', 'User.Read'];
  async sendEmailWithOutlookDraft() {
    // Store the current signature data before generating the PDF
    // We store this to restore it later, even though we don't use it directly
    try {
      const activeAccount = this.msalService.instance.getActiveAccount();

      if (!activeAccount) {
        this.msalService.loginRedirect({ scopes: this.scopes });
        return;
      }

      const tokenResult = await this.msalService.instance.acquireTokenSilent({
        account: activeAccount,
        scopes: this.scopes
      });

      const accessToken = tokenResult.accessToken;
      const headers = new HttpHeaders({
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      });

      // 1. Generate the PDF
      if (!this.formHtmlView) {
        this.shareService.showError('Form HTML view not available');
        return;
      }

      const pdfBytes = await this.formHtmlView.generatePdf(true);
      if (!pdfBytes) {
        console.error('PDF generation failed.');
        this.shareService.showError('Failed to generate PDF for email');
        return;
      }

      // Get the form name and sanitize it for the file name
      let formName = this.formData?.auditHistory.formName || 'form';

      // Remove leading/trailing spaces and invalid characters
      formName = formName.trim();

      // Replace invalid characters with underscores
      formName = formName.replace(/[\\/:*?"<>|]/g, '_');

      // Ensure the name is not empty
      if (formName.length === 0) {
        formName = 'form';
      }

      const fileName = `${formName}.pdf`;
      console.log('Creating email attachment with name:', fileName);

      const base64Pdf = await this.arrayBufferToBase64(pdfBytes);

      // 2. Create the draft with attachment
      const draftPayload = {
        subject: `${this.formData?.auditHistory.formName || 'Form'} Document`,
        body: {
          contentType: 'Text',
          content: 'Please find the attached document.'
        },
        toRecipients: [],
        attachments: [
          {
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: fileName,
            contentType: "application/pdf",
            contentBytes: base64Pdf
          }
        ]
      };

      const draftResponse: any = await firstValueFrom(
        this.http.post('https://graph.microsoft.com/v1.0/me/messages', draftPayload, { headers })
      );
      const draftWebLink = draftResponse.webLink;
      console.log('Draft created with ID:', draftResponse);

      if (this.isMobileDevice()) {

        const outlookAppLink = 'ms-outlook://';
        window.location.href = outlookAppLink;

        // Optionally, show a toast:
        setTimeout(() => {
          this.shareService.showInfo('Outlook app opened. Please check your Drafts folder.');
        }, 500);

      } else {
        // Desktop: Open draft in Outlook Web (read-only, due to Microsoft limitation)
        window.open(draftWebLink, "_blank");
      }

    } catch (error) {
      console.error('Error sending email with draft:', error);
      this.shareService.showError('Error preparing email: ' + (error as Error).message);
    }
  }

  isMobileDevice(): boolean {
    return /Mobi|Android|iPhone|iPad/i.test(navigator.userAgent);
  }

  showOneDrivePopup() {
    if (!this.formHtmlView) {
      this.shareService.showError('Form HTML view not available');
      return;
    }

    // Generate PDF first
    this.formHtmlView.generatePdf(true)
      .then((pdfBytes: Uint8Array | void) => {
        if (!pdfBytes) {
          this.shareService.showError('Failed to generate PDF for OneDrive upload');
          return;
        }

        try {
          // Get the form name and sanitize it for the file name
          let formName = this.formData?.auditHistory.formName || 'form';
          formName = formName.trim().replace(/[\\/:*?"<>|]/g, '_');
          if (formName.length === 0) {
            formName = 'form';
          }

          const fileName = `${formName}.pdf`;
          const bytes = new Uint8Array(pdfBytes);
          const blob = new Blob([bytes], { type: 'application/pdf' });
          this.pdfFile = new File([blob], fileName, { type: 'application/pdf' });

          // Show the OneDrive dialog
          this.showOneDriveDialog = true;

          // Load OneDrive folders
          this.loadOneDriveFolders();

          // Setup subscriptions to OneDrive service observables
          this.setupOneDriveSubscriptions();

        } catch (error) {
          console.error('Error creating file for OneDrive:', error);
          this.shareService.showError('Error preparing file for OneDrive upload');
        }
      })
      .catch(error => {
        console.error('Error generating PDF:', error);
        this.shareService.showError('Error generating PDF: ' + (error as Error).message);
      });
  }

  hideOneDrivePopup() {
    this.showOneDriveDialog = false;
    this.selectedFolderId = '';
    this.oneDriveError = '';
    this.isUploading = false;
    this.uploadProgress = 0;
    this.currentFolderId = 'Home';
    this.folderPath = [];
    this.showingNewFolderInput = false;
    this.newFolderName = '';

    // Reset folder expansion state
    this.folders.forEach(folder => {
      folder.isExpanded = false;
    });
  }

  setupOneDriveSubscriptions() {
    // Clear any existing subscriptions
    this.oneDriveSubscriptions.forEach(sub => sub.unsubscribe());
    this.oneDriveSubscriptions = [];

    // Subscribe to loading state
    this.oneDriveSubscriptions.push(
      this.oneDriveService.loading$.subscribe(isLoading => {
        this.isLoadingFolders = isLoading;
      })
    );

    // Subscribe to error messages
    this.oneDriveSubscriptions.push(
      this.oneDriveService.error$.subscribe(error => {
        if (error) {
          console.log('OneDrive error:', error);
          this.oneDriveError = error;
        } else {
          this.oneDriveError = '';
        }
      })
    );

    // Subscribe to folders
    this.oneDriveSubscriptions.push(
      this.oneDriveService.folders$.subscribe(folders => {
        this.folders = folders;
        this.displayedFolders = folders;
      })
    );

    // Subscribe to upload progress
    this.oneDriveSubscriptions.push(
      this.oneDriveService.uploadProgress$.subscribe(progress => {
        this.isUploading = progress.isLoading;
        this.uploadProgress = progress.progress;

        if (progress.error) {
          this.shareService.showError(progress.error);
        }

        if (progress.progress === 100 && !progress.error) {
          setTimeout(() => {
            this.hideOneDrivePopup();
            this.shareService.showSuccess('File uploaded successfully to OneDrive');
          }, 500);
        }
      })
    );
  }

  loadOneDriveFolders() {
    if (this.currentFolderId === 'Home') {
      this.folderPath = [];
    }

    this.oneDriveService.loadOneDriveFolders().subscribe({
      next: (folders) => {
        this.folders = folders;
        this.displayedFolders = folders;

        if (folders.length === 0) {
          this.shareService.showWarning('No folders found in your OneDrive');
        }
      },
      error: (error) => {
        console.error('Error loading folders:', error);
        this.shareService.showError('Failed to load OneDrive folders');
      }
    });
  }

  selectFolder(folder: OneDriveFolder) {
    if (!folder) {
      console.error('Attempted to select a null or undefined folder');
      return;
    }

    console.log('Selected folder:', folder.name, 'with ID:', folder.id);
    this.selectedFolderId = folder.id;

    if (folder.id === 'Home') {
      this.shareService.showWarning('Please select a specific folder, not the root.');
    }
  }

  navigateToPathItem(index: number) {
    if (index < 0 || index >= this.folderPath.length) {
      return;
    }

    const folder = this.folderPath[index];
    folder.isLoading = true;
    this.currentFolderId = folder.id;

    // Truncate the path to this point
    this.folderPath = this.folderPath.slice(0, index + 1);

    // If it's the last item in the path, we're already there
    if (index === this.folderPath.length - 1) {
      folder.isLoading = false;
      return;
    }

    // Load the subfolders of this folder
    this.oneDriveService.loadSubfolders(folder.id).subscribe({
      next: (subfolders) => {
        this.displayedFolders = subfolders;
        folder.isLoading = false;

        if (subfolders.length === 0) {
          this.shareService.showInfo(`No subfolders found in "${folder.name}"`);
        }
      },
      error: (error) => {
        folder.isLoading = false;
        console.error('Error loading subfolders:', error);
        this.shareService.showError(`Failed to load subfolders for "${folder.name}"`);
      }
    });
  }

  uploadToSelectedFolder() {
    if (!this.pdfFile) {
      this.shareService.showError('No file to upload');
      return;
    }

    const targetFolderId = this.selectedFolderId || this.currentFolderId;
    if (!targetFolderId) {
      this.shareService.showError('Please select a folder to upload to');
      return;
    }

    if (targetFolderId === 'Home') {
      this.shareService.showError('Please select a specific folder, not the root');
      return;
    }

    this.isUploading = true;
    this.uploadProgress = 10;

    this.oneDriveService.uploadToSelectedFolder(this.pdfFile, targetFolderId)
      .then(webUrl => {
        setTimeout(() => {
          this.hideOneDrivePopup();
          this.shareService.showSuccess('File uploaded successfully to OneDrive');
          if (webUrl) {
            window.open(webUrl, '_blank');
          }
        }, 500);
      })
      .catch(error => {
        console.error('Error uploading file:', error);
        this.shareService.showError('Upload failed: ' + (error.message || 'Unknown error'));
        this.isUploading = false;
        this.uploadProgress = 0;
      });
  }

  // Add new methods for OneDrive UI
  switchAccount() {
    // Store the current active account ID to compare later
    const currentAccountId = this.msalService.instance.getActiveAccount()?.homeAccountId;

    // Show loading indicator
    this.isLoadingFolders = true;
    this.oneDriveError = '';

    // Force account selection by passing true to login method
    this.oneDriveService.login(true).subscribe({
      next: (loginResponse: any) => {
        // Check if the user selected the same account
        const selectedAccountId = loginResponse.account.homeAccountId;
        const isSameAccount = currentAccountId === selectedAccountId;

        console.log('Account switch:', isSameAccount ? 'Same account selected' : 'New account selected');

        // Set the selected account as active
        this.msalService.instance.setActiveAccount(loginResponse.account);

        // Clear folders and selected folder



        this.folders = [];
        this.selectedFolderId = '';

        // Reload folders with the selected account
        this.oneDriveService.loadOneDriveFolders().subscribe({
          next: (_) => {
            // This will be handled by the subscription in setupOneDriveSubscriptions
            if (isSameAccount) {
              console.log('Refreshed folders for the same account');
            } else {
              console.log('Loaded folders for new account');
            }
          },
          error: (error) => {
            console.error('Error loading folders after account switch:', error);
            this.shareService.showError('Failed to load folders after account switch');
            this.isLoadingFolders = false; // Ensure loading indicator is hidden on error
          }
        });
      },
      error: (error: any) => {
        this.isLoadingFolders = false;
        this.oneDriveError = 'Account selection failed: ' + (error.message || 'Unknown error');
      }
    });
  }

  showNewFolderInput() {
    this.showingNewFolderInput = true;
    this.newFolderName = '';
  }

  cancelNewFolder() {
    this.showingNewFolderInput = false;
    this.newFolderName = '';
  }

  async createNewFolder() {
    if (!this.newFolderName.trim()) {
      return;
    }

    try {
      const folder = await this.oneDriveService.createFolder(this.newFolderName, this.currentFolderId);
      this.shareService.showSuccess('Folder created successfully');
      this.showingNewFolderInput = false;
      this.newFolderName = '';
      this.loadOneDriveFolders();
    } catch (error) {
      console.error('Error creating folder:', error);
      this.shareService.showError('Failed to create folder: ' + (error as Error).message);
    }
  }

  getCurrentFolderName(): string {
    if (this.currentFolderId === 'Home') {
      return 'Home';
    }
    const currentFolder = this.folders.find(f => f.id === this.currentFolderId);
    return currentFolder ? currentFolder.name : 'Unknown Folder';
  }

  getSelectedFolderName(): string {
    if (!this.selectedFolderId || this.selectedFolderId === 'Home') {
      return '';
    }
    const selectedFolder = this.folders.find(f => f.id === this.selectedFolderId);
    return selectedFolder ? selectedFolder.name : 'Unknown Folder';
  }

  navigateToRoot() {
    this.currentFolderId = 'Home';
    this.folderPath = [];
    this.loadOneDriveFolders();
  }

  navigateToFolder(folder: OneDriveFolder) {
    if (folder.isLoading) {
      return;
    }

    folder.isLoading = true;
    this.currentFolderId = folder.id;
    this.folderPath.push(folder);

    this.oneDriveService.loadSubfolders(folder.id).subscribe({
      next: (subfolders) => {
        this.displayedFolders = subfolders;
        folder.isLoading = false;
        folder.isExpanded = true;
        folder.children = subfolders;

        if (subfolders.length === 0) {
          this.shareService.showInfo(`No subfolders found in "${folder.name}"`);
        }
      },
      error: (error) => {
        folder.isLoading = false;
        console.error('Error loading subfolders:', error);
        this.shareService.showError(`Failed to load subfolders for "${folder.name}"`);
      }
    });
  }
}
