<div class="forms-container">
  <!-- Sidebar -->
  <div class="sidebar" [class.collapsed]="isSidebarCollapsed">
    <!-- Forms List View -->
    <div *ngIf="sidebarView === 'forms'">
      <div class="sidebar-header">
        <h3>Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="forms-list">
          <li *ngFor="let form of this.shareService.FromList" class="form-item">
            <div class="form-header"
                 [class.active]="selectedFormId === form.id"
                 (click)="selectForm(form)">
              <span class="form-name">{{ form.auditHistory.formName || 'Unnamed Form' }}</span>
              <i class="fas fa-chevron-right"></i>
            </div>
          </li>
        </ul>
        <div *ngIf="this.shareService.FromList.length === 0" class="no-forms">
          No forms available
        </div>
      </div>
    </div>

    <!-- Form Types View -->
    <div *ngIf="sidebarView === 'types'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToForms()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>{{ currentFormForTypes?.auditHistory?.formName || 'Form Types' }}</h3>
      </div>
      <div class="sidebar-content">
        <ul class="form-types-list">
          <li *ngFor="let type of formTypes"
              class="form-type-item"
              (click)="selectFormType(type)">
            <span class="form-type-name">{{ type }}</span>
            <i class="fas fa-chevron-right" style="color: lightgray;"></i>
          </li>
        </ul>
      </div>
    </div>

    <!-- Form Submissions View -->
    <div *ngIf="sidebarView === 'submissions'">
      <div class="sidebar-header">
        <button class="back-button" (click)="backToFormTypes()">
          <i class="fas fa-arrow-left"></i>
        </button>
        <h3>Submitted Forms</h3>
      </div>
      <div class="sidebar-content">
        <ul class="submissions-list">
          <li *ngFor="let submission of formSubmissions"
              class="submission-item"
              [class.active]="selectedSubmissionId === submission.id"
              (click)="selectSubmission(submission)">
            <div class="submission-header">
              <!-- <span class="submission-id">ID: {{ submission.id }}</span> -->
              <span class="submission-date">{{ submission.auditHistory.updatedDate | date:'short' }}</span>
            </div>
            <div class="submission-info">
              <span class="submission-by">By: {{ submission.auditHistory.updatedBy || 'Dev One' }}</span>
            </div>
          </li>
        </ul>
        <div *ngIf="formSubmissions.length === 0" class="no-submissions">
          <i class="fas fa-info-circle"></i>
          No data submitted for this form
        </div>
      </div>
    </div>

    <!-- Dashboard View -->
    <div *ngIf="sidebarView === 'dashboard'">
      <div class="sidebar-header">
        <!-- <button class="back-button" (click)="backToForms()">
          <i class="fas fa-arrow-left"></i>
        </button> -->
        <h3>Dashboard</h3>
      </div>
      <div class="sidebar-content">
        <ul class="dashboard-forms-list">
          <li *ngFor="let formStat of formStats"
              class="dashboard-form-item submission-item"
              [class.active]="selectedFormForChart?.formName === formStat.formName"
              (click)="selectFormForChart(formStat)">
            <div class="dashboard-form-header">
              <span class="dashboard-form-name">{{ formStat.formName }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="main-content" [class.expanded]="isSidebarCollapsed">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" (click)="toggleSidebar()">
      <i class="fas" [ngClass]="{'fa-chevron-right': isSidebarCollapsed, 'fa-chevron-left': !isSidebarCollapsed}"></i>
    </button>

    <!-- Form Content -->
    <ng-container *ngIf="sidebarView !== 'dashboard'">
      <div *ngIf="selectedFormId" class="form-details">
        <!-- Pass both formId and submissionId (if available) to the home component -->
        <app-home
          [formId]="selectedFormId || ''"
          [submissionId]="selectedSubmissionId || ''">
        </app-home>
      </div>
      <div *ngIf="!selectedFormId" class="no-selection">
        <p>Select a form from the sidebar to view details</p>
      </div>
    </ng-container>

    <!-- Dashboard Content -->
    <div *ngIf="sidebarView === 'dashboard'" class="dashboard-container">

      <!-- Dashboard Cards -->
      <div class="dashboard-cards">
        <div class="dashboard-card" *ngFor="let card of dashboardCards">
          <div class="card-icon">
            <i class="fas {{ card.icon }}"></i>
          </div>
          <div class="card-content">
            <h3 class="card-title">{{ card.title }}</h3>
            <p class="card-value">{{ card.value }}</p>
          </div>
        </div>
      </div>

      <!-- Time-based Chart Section -->
      <div class="dashboard-chart-container" *ngIf="selectedFormForChart">
        <div class="chart-header">
          <h2 class="dashboard-section-title">{{ selectedFormForChart.formName }} Submissions Over Time</h2>
          <div class="date-range-controls">
            <div class="date-control">
              <label for="startDate">From:</label>
              <input
                type="date"
                id="startDate"
                [ngModel]="startDate | date:'yyyy-MM-dd'"
                (ngModelChange)="startDate = $event; updateDateRange()"
                class="date-input">
            </div>
            <div class="date-control">
              <label for="endDate">To:</label>
              <input
                type="date"
                id="endDate"
                [ngModel]="endDate | date:'yyyy-MM-dd'"
                (ngModelChange)="endDate = $event; updateDateRange()"
                class="date-input">
            </div>
          </div>
        </div>
        <div class="chart-content-container">
          <div class="chart-wrapper">
            <canvas #timeChart baseChart
              [type]="timeChartType"
              [data]="timeChartData"
              [options]="timeChartOptions">
            </canvas>
          </div>

          <div class="chart-details">
            <div class="chart-stats-card">
              <h3 class="stats-title">Form Statistics</h3>
              <div class="stats-item">
                <span class="stats-label">Total Submissions:</span>
                <span class="stats-value">{{ selectedFormForChart.submissionCount }}</span>
              </div>
              <div class="stats-item" *ngIf="selectedFormForChart.lastSubmitted">
                <span class="stats-label">Last Submission:</span>
                <span class="stats-value">{{ selectedFormForChart.lastSubmitted | date:'medium' }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">Submissions in Range:</span>
                <span class="stats-value">{{ submissionsInDateRange }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">Date Range:</span>
                <span class="stats-value">{{ startDate | date:'mediumDate' }} <strong>to</strong> {{ endDate | date:'mediumDate' }}</span>
              </div>
            </div>

            <div class="chart-stats-card" *ngIf="submissionsByDate.length > 0">
              <h3 class="stats-title">Daily Breakdown</h3>
              <div class="daily-stats-list">
                <div class="daily-stats-item" *ngFor="let item of submissionsByDate.slice().reverse().slice(0, 5)">
                  <span class="stats-date">{{ item.date | date:'mediumDate' }}</span>
                  <span class="stats-count">{{ item.count }} submissions</span>
                </div>
              </div>
              <div class="stats-footer" *ngIf="submissionsByDate.length > 5">
                <span>Showing 5 of {{ submissionsByDate.length }} days</span>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="submissionsByDate.length === 0" class="no-data-message">
          <i class="fas fa-info-circle"></i>
          No submissions found in the selected date range
        </div>
      </div>

      <!-- Submission Details Section -->
      <div *ngFor="let date of groupedSubmissions | keyvalue: sortByDate">
        <div class="date-header">
           <span class="date-text">{{ date.key | date:'mediumDate' }}</span>
            <span class="date-count">{{ date.value.length }}</span>
        </div>

        <div *ngFor="let sub of date.value" class="submission-row">
          <span class="time">{{ sub.time }}</span>
          <span class="title">{{ sub.createdBy }}</span>
          <span class="location">{{ sub.location }}</span>
          <span class="link-icon" title="View form-details">
            <i class="fas fa-link" (click)="formViewLink(sub.formId, sub.submissionId)"></i>
          </span>
          <span class="flag-item" [class.hidden]="!sub.hasFollowUp" title="This submission has a follow-up">
            <i class="fas fa-flag"></i>
          </span>
        </div>
      </div>


    </div>
  </div>
</div>
