/* You can add global styles to this file, and also import other style files */
@import "ngx-toastr/toastr";
* {
  box-sizing: border-box;
  font-family: '<PERSON><PERSON><PERSON>';
}

body {
  background-color: rgb(238, 238, 238);
}

/* Common Button Styles */
.btn-primary {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background-color: #45a049;
}

.btn-secondary {
  padding: 10px 20px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-secondary:hover {
  background-color: #e5e5e5;
}

/* Common Form Styles */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #4CAF50;
}

/* Common Icon Button Styles */
.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

/* Common Add Button Styles (for Add Item and Add Section) */
.btn-add-small {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background-color: #4CAF50;
  border: none;
  border-radius: 4px;
  color: white;
  text-align: center;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  transition: all 0.2s;
  height: 28px;
}

.btn-add-small:hover {
  background-color: #45a049;
}

.btn-add-small i {
  margin-right: 6px;
  font-size: 12px;
}

/* Save Form Button */
.btn-save {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #4CAF50;
  border: none;
  border-radius: 4px;
  color: white;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-save:hover {
  background-color: #45a049;
}

.btn-save i {
  margin-right: 8px;
}

/* Responsive Breakpoints */
@media (max-width: 768px) {
  .btn-primary, .btn-secondary {
    padding: 8px 16px;
    font-size: 14px;
  }

  .btn-add-small {
    padding: 3px 10px;
    font-size: 12px;
    height: 26px;
  }

  .btn-save {
    padding: 6px 12px;
    font-size: 13px;
  }

  .form-group input {
    padding: 8px;
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .btn-primary, .btn-secondary {
    width: 100%;
    margin-bottom: 10px;
  }
}


    /* Full Window Mode Styles - Matching home.component.css */
.full-window-mode {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: white;
      z-index: 1000;
      overflow-y: auto;
    }

    .full-window-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 1001;
    }

    .full-window-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .Action-btn {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 10px;
    }

    .action-button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      padding: 8px 16px;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .action-button:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-1px);
    }

    .action-text {
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .shared-form-container {
        padding: 0;
      }

      .full-window-header {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .full-window-title {
        font-size: 20px;
      }

      .Action-btn {
        flex-wrap: wrap;
        justify-content: center;
      }

      .action-button {
        padding: 10px 12px;
        font-size: 12px;
      }

      .action-text {
        display: none;
      }
    }

/*  Side-----------------------------------------BArs******************** */
.sidebar1{
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #0973c9 100%);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.sidebar1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  /* backdrop-filter: blur(10px); */
  z-index: 1;
}

.sidebar-header1 {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.header-icon1 {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  /* backdrop-filter: blur(10px); */
}

.header-icon1 i {
  font-size: 20px;
  color: white;
}

.sidebar-header1 h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sidebar-content1 {
  padding: 20px 0;
  position: relative;
  z-index: 2;
}

.sidebar-nav1 {
  padding: 0 16px;
}

.nav-list1 {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item1 {
  margin-bottom: 8px;
}

.nav-link1 {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  /* backdrop-filter: blur(10px); */
}

.nav-link1:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(4px);
}

.nav-link1.active {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-icon1 {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.nav-icon1 i {
  font-size: 16px;
}

.nav-text1 {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.nav-indicator1 {
  width: 4px;
  height: 20px;
  background: white;
  border-radius: 2px;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Main Content Styles */
.main-content1 {
  flex: 1;
  padding: 15px;
  background-color: #f8fafc;
  overflow-y: auto;
  min-height: 100%;
}
.content-section1 {
  max-width: 100%;
  margin: 0;
}

.section-header1 {
  margin-bottom: 15px;
}

.section-title1 {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.section-title1 i {
  font-size: 24px;
  color: #667eea;
  margin-right: 12px;
}

.section-title1 h2 {
  margin: 0;
  font-size: 1.7rem;
  font-weight: 700;
  color: #1a202c;
}

.section-description1 {
  margin: 0;
  font-size: 1.1rem;
  color: #64748b;
  font-weight: 400;
}
.content-card1 {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: auto;
}

.content-card1:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  /* transform: translateY(-2px); */
}

.card-content1 {
  padding: 1.5rem;
}

/* One Drive popup Style css */
  /* OneDrive Popup Styles */
.onedrive-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.onedrive-popup {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.onedrive-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #0078d4;
  color: white;
}

.onedrive-popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.account-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.account-btn i {
  margin-right: 6px;
}

.account-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.onedrive-popup-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  min-height: 200px;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.spinner {
  border: 4px solid rgba(0, 120, 212, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0078d4;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-messages {
  background-color: #fdeded;
  color: #d83b01;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.error-messages i {
  margin-right: 10px;
}

.retry-btn {
  background-color: #d83b01;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  margin-top: 10px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #c23400;
}

.folder-list h4 {
  margin-top: 0;
  margin-bottom: 0;
  color: #333;
  font-weight: 500;
}

.folder-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.new-folder-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.new-folder-btn i {
  margin-right: 6px;
  color: #0078d4;
}

.new-folder-btn:hover {
  background-color: #e0e0e0;
}

.new-folder-input {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.new-folder-location {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 14px;
}

.new-folder-location span {
  color: #666;
}

.new-folder-location strong {
  color: #0078d4;
}

.new-folder-input input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 10px;
}

.new-folder-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.new-folder-buttons button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.new-folder-buttons button:first-child {
  background-color: #0078d4;
  color: white;
}

.new-folder-buttons button:first-child:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.new-folder-buttons button:last-child {
  background-color: #f0f0f0;
}

.folder-path {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  overflow-x: auto;
  white-space: nowrap;
}

.folder-path span {
  cursor: pointer;
  color: #0078d4;
}

.folder-path i {
  margin: 0 8px;
  color: #666;
}

.folder-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.folder-tree {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.folder-item, .subfolder-item {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
  position: relative;
  cursor: pointer;
}

.folder-item:hover, .subfolder-item:hover {
  background-color: #f5f5f5;
}



.folder-navigate-btn {
  position: absolute;
  right: 10px;
  background-color: transparent;
  border: none;
  color: #0078d4;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.folder-navigate-btn:hover {
  background-color: rgba(0, 120, 212, 0.1);
}

.folder-expand {
  width: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
  cursor: pointer;
  color: #0078d4;
}

.folder-expand:hover {
  color: #005a9e;
}

.folder-name {
  margin-left: 8px;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-tree li {
  margin: 0;
}

.folder-tree li.selected > .folder-item {
  background-color: #e6f2fa;
  color: #0078d4;
}

.folder-tree li.expanded > .folder-item {
  background-color: #f9f9f9;
}

.subfolder-list {
  list-style: none;
  padding-left: 25px !important;
  margin: 0;
  border: none !important;
  border-radius: 0 !important;
  max-height: none !important;
}

.subfolder-list li {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}

.subfolder-list li.selected .subfolder-item {
  background-color: #e6f2fa;
  color: #0078d4;
}

.subfolder-item i {
  margin-right: 10px;
  color: #0078d4;
  cursor: pointer;
}

.subfolder-item .folder-name {
  cursor: pointer;
}

.no-folders {
  text-align: center;
  padding: 30px;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.upload-progress {
  margin-top: 20px;
}

.progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress {
  height: 100%;
  background-color: #0078d4;
  transition: width 0.3s ease;
}

.onedrive-popup-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.upload-btn {
  background-color: #0078d4;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.upload-btn i {
  margin-right: 8px;
}

.upload-path {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

.upload-btn:hover {
  background-color: #006cbe;
}

.upload-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

@media (max-width: 767px) {
  .onedrive-popup {
    width: 95%;
    max-height: 80vh;
  }

  .onedrive-popup-header {
    padding: 12px 15px;
  }

  .onedrive-popup-content {
    padding: 15px;
  }

  .folder-list ul {
    max-height: 200px;
  }

  .folder-list li {
    padding: 10px 12px;
  }

  .onedrive-popup-footer {
    padding: 12px 15px;
  }

  .cancel-btn, .upload-btn {
    padding: 8px 12px;
    font-size: 14px;
  }
}


