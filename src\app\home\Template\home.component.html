  <div class="form-name-bar">
    <div class="form-name-container">
      <h2 class="form-name-title">{{ demo2 && demo2.auditHistory ? demo2.auditHistory.formName : 'Form' }}</h2>
    </div>
    <!-- form-actions -->
    <div class="">
      <!-- Show share icon for new forms (no submissionId) -->
      <button *ngIf="!submissionId" type="button" class="share-btn" (click)="generateFormShareLink()" title="Share Form">
        <i class="fas fa-share-alt"></i>
      </button>
      <!-- Show eye icon for existing submissions -->
      <button *ngIf="submissionId" type="button" class="eye" (click)="toggleFullWindowMode()" title="Full view">
        <i class="fas fa-eye"></i>
      </button>
    </div>
  </div>
<div class="form-container" [ngClass]="{'centered-form': isNewFormFromLink}">
  <!-- Skeleton Loader -->
  <app-form-skeleton *ngIf="shareService.showSkeleton && !isFullWindowMode" [sections]="3" [fieldsPerSection]="5"></app-form-skeleton>

  <!-- Full Window Mode HTML View -->
  <div *ngIf="isFullWindowMode && !shareService.showSkeleton" class="full-window-mode">
    <div class="full-window-header">
      <div class="full-window-title">
        {{ demo2 && demo2.auditHistory ? demo2.auditHistory.formName : 'Form' }}
      </div>
      <div class="header-actions">
        <ul class="Action-btn">
          <li class="action-button" (click)="formHtmlView.generatePdf(false)" Title="Download PDF">
            <span class="action-text">Download PDF</span>
          </li>
          <li class="action-button"  Title="Copie Link" (click)="generateHTMLViewShareableLink()" *ngIf="submissionId">
            <span class="action-text">Share Form via Link</span>
          </li>
          <li class="action-button" (click)="showOneDrivePopup()" Title="Save in One Drive">
            <span class="action-text">Save in One Drive</span>
          </li>
          <li class="action-button" (click)="sendEmailWithOutlookDraft()"  Title="Send via Email">
            <span class="action-text">Send via Mail</span>
          </li>
        </ul>
        <button class="close-full-window" (click)="toggleFullWindowMode()" title="Close View">×</button>
      </div>
    </div>
    <app-form-html-view
      #formHtmlView
      [Data]="formGroup.value"
      [formJson]="demo2"
      [loadedImages]="uploadedImages | keyValueToArray"
      [signatureImage]="pendingSignatureData"
      [guidanceImages]="guidanceImages"
      [formComponents]="demo2.component || []">
    </app-form-html-view>
  </div>

  <!-- Dynamic Form -->
  <form *ngIf="!shareService.showSkeleton && !isFullWindowMode" [formGroup]="formGroup" (ngSubmit)="onSubmit()" >
    <!-- Form Details Section (First Section) -->
    <div class="form-section" >
      <div class="form-details-content">
        <div class="metadata-row">
          <span class="metadata-label">User:</span>
          <span class="metadata-value">{{ demo2.auditHistory.userName }}</span>
        </div>
        <div class="metadata-row">
          <span class="metadata-label">Location:</span>
          <span class="metadata-value">{{ demo2.auditHistory.location }}</span>
        </div>
        <div class="metadata-row">
          <span class="metadata-label">Created By:</span>
          <span class="metadata-value">Dev One</span>
        </div>
        <!-- <div class="metadata-row" *ngIf="demo2.updatedDate">
          <span class="metadata-label">Last Updated:</span>
          <span class="metadata-value">{{ demo2.updatedDate }}</span>
        </div> -->
      </div>
    </div>

    <!-- Dynamic Form Sections -->
    <div *ngFor="let section of demo2.component; let i = index" class="form-section">
      <div class="section-header">
        <h3 (click)="toggleSectionCollapse(section)" [class.collapsible]="section.canCollapsed">
          {{ section.title }}
          <span *ngIf="section.canCollapsed" class="collapse-icon">
            <i class="fas" [ngClass]="{'fa-chevron-down': !section.isCollapsed, 'fa-chevron-up': section.isCollapsed}"></i>
          </span>
        </h3>
        <button *ngIf="section.repeatable" type="button" class="repeat-section-btn" (click)="repeatSection(i)">
          <i class="fas fa-plus"></i> Repeat Section
        </button>
      </div>
      <ng-container *ngIf="!section.isCollapsed">
        <ng-container *ngFor="let component of section.elements;">
        <!-- Input Fields -->
        <div *ngIf="component.type === 'text'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
              <input
              [type]="component.type"
              [formControlName]="component.attributes.field_name??''"
              [placeholder]="component.attributes.placeholder_text??''"
              [attr.maxlength]="component.attributes.validations?.maxlength"
              [attr.minlength]="component.attributes.validations?.minlength"/>
                 <div *ngIf="formGroup.get(component.attributes.field_name ?? '')?.invalid && formGroup.get(component.attributes.field_name ?? '')?.touched" class="error-message">
                   {{ getErrorMessage(component.attributes.field_name || '') }}
                 </div>

                 <div class="action-icons" *ngIf="component.attributes.actions">
                  <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
                    <i class="fas fa-comment-alt"></i>
                  </a>
                  <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
                    <i class="fas fa-camera"></i>
                  </a>
                  <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
                    <i class="fas fa-flag"></i>
                  </a>
                  <input
                    type="file"
                    accept=".jpg, .jpeg, .png"
                    [id]="'file_' + component.attributes.field_name"
                    style="display: none"
                    (change)="onFileSelected($event, component)"
                  />
                </div>
                <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
                  <!-- <i class="fas fa-camera"></i> -->
                  <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
                </div>
                  <!-- Commnet  -->
                <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
                  <textarea
                    [formControlName]="component.attributes.field_name + '_comment'"
                    [rows]="3"
                    placeholder="Add your comment"
                    class="comment-box"
                  ></textarea>
                </div>
                <div *ngIf="component.attributes.field_name && selectedFollowUp[component.attributes.field_name]">
                    <span class="selectedFollowUp">{{selectedFollowUp[component.attributes.field_name]}}</span>
                </div>
                <div *ngIf="visibleflag[component.attributes.field_name + '_flag']">
                  <ul>
                    <li class="flag-item"
                      *ngFor="let item of FollowUp"
                      (click)="selectItem(item ,component)"
                      [class.selected]="component.attributes.field_name && item === this.selectedFollowUp[component.attributes.field_name]">
                      {{ item }}
                    </li>
                  </ul>
                </div>
        </div>

        <!-- Textarea -->
        <div *ngIf="component.type === 'textarea'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <textarea
            [formControlName]="component.attributes.field_name || ''"
            [placeholder]="component.attributes.placeholder_text??''"
            [rows]="5"
            class="comment-box"></textarea>
          <div *ngIf="formGroup.get(component.attributes.field_name ?? '')?.invalid && formGroup.get(component.attributes.field_name ?? '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name || '') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
                    type="file"

                    accept=".jpg, .jpeg, .png"
                    [id]="'file_' + component.attributes.field_name"
                    style="display: none"
                    (change)="onFileSelected($event, component)"
                  />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- Date Picker -->
        <div *ngIf="component.type === 'date'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <input
            type="date"
            class="date-input"
            [formControlName]="component.attributes.field_name || ''"
            [attr.min]="component.attributes.validations?.minDate"
            [attr.max]="component.attributes.validations?.maxDate"
            [required]="component.attributes.is_required??false"
          />
          <div *ngIf="formGroup.get(component.attributes.field_name ?? '')?.invalid && formGroup.get(component.attributes.field_name ?? '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name??'') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
              type="file"

              accept=".jpg, .jpeg, .png"
              [id]="'file_' + component.attributes.field_name"
              style="display: none"
              (change)="onFileSelected($event, component)"
            />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- GroupedDropdown -->
        <div *ngIf="component.type === 'Select'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <app-grouped-dropdown
            [Label]="component.attributes.label ||''"
            [formGroup]="formGroup"
            [fieldName]="component.attributes.field_name===undefined?'':component.attributes.field_name"
            [placeholder]="component.attributes.placeholder_text === undefined ? '' : component.attributes.placeholder_text"
            [group]="component.attributes.dataSource  || { list: [] }"
            [isMultiSelect]="component.multiselect??false">
        </app-grouped-dropdown>
        <div class="action-icons" *ngIf="component.attributes.actions">
          <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
            <i class="fas fa-comment-alt"></i>
          </a>
          <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
            <i class="fas fa-camera"></i>
          </a>
          <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
            <i class="fas fa-flag"></i>
          </a>
          <input
            type="file"

            accept=".jpg, .jpeg, .png"
            [id]="'file_' + component.attributes.field_name"
            style="display: none"
            (change)="onFileSelected($event, component)"
          />
        </div>
        <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
          <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
        </div>
          <!-- Commnet  -->
          <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
            <textarea
              [formControlName]="component.attributes.field_name + '_comment'"
              [rows]="3"
              placeholder="Add your comment"
              class="comment-box"
            ></textarea>
          </div>
        </div>

        <!-- Signature Pad -->
        <div *ngIf="component.type === 'signature'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <canvas #signaturePad class="signature-pad"></canvas>
          <label>Select Pen Color:</label>
          <input type="color" (change)="changePenColor($event)" [value]="signatureColor" />
          <button type="button" (click)="clearSignature()">Clear</button>
          <button type="button" (click)="saveSignature(component.attributes.label ?? 'signature')">Save</button>
          <div *ngIf="formGroup.get(component.attributes.field_name ?? '')?.invalid && formGroup.get(component.attributes.field_name ?? '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name ?? '') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
              type="file"
              accept=".jpg, .jpeg, .png"
              [id]="'file_' + component.attributes.field_name"
              style="display: none"
              (change)="onFileSelected($event, component)"/>
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- Image Upload -->
        <div *ngIf="component.type === 'file'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <input type="file"
                 accept=".jpg, .jpeg, .png"
                 (change)="onFileSelect($event, component.attributes.field_name || '')"
                 [attr.placeholder]="component.attributes.placeholder_text" />
          <div *ngIf="previewImageUrl || component.attributes.imageDisplayUrl">
            <img [src]="component.attributes.imageDisplayUrl || previewImageUrl" alt="Uploaded Image" width="150" />
          </div>
          <div *ngIf="formGroup.get(component.attributes.field_name || '')?.hasError('invalidFileType')" class="error-message">
            {{ getErrorMessage(component.attributes.field_name || '') }}
          </div>
          <div *ngIf="formGroup.get(component.attributes.field_name || '')?.hasError('fileTooLarge')" class="error-message">
            {{ getErrorMessage(component.attributes.field_name || '') }}
          </div>
          <div *ngIf="formGroup.get(component.attributes.field_name || '')?.invalid && formGroup.get(component.attributes.field_name || '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name || '') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
            type="file"

            accept=".jpg, .jpeg, .png"
            [id]="'file_' + component.attributes.field_name"
            style="display: none"
            (change)="onFileSelected($event, component)"
          />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- Map -->
         <div *ngIf="component.type === 'map'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <app-map
          [defaultLat]="component.attributes.default_lat===undefined?0:component.attributes.default_lat"
          [defaultLng]="component.attributes.default_lng===undefined?0:component.attributes.default_lng"
          (locationSelected)="updateLocation($event, component.attributes.field_name??'')"
          ></app-map>
          <div *ngIf="formGroup.get(component.attributes.field_name ?? '')?.invalid && formGroup.get(component.attributes.field_name ?? '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name ?? '') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
            type="file"

            accept=".jpg, .jpeg, .png"
            [id]="'file_' + component.attributes.field_name"
            style="display: none"
            (change)="onFileSelected($event, component)"
          />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="5"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>



        <!-- hyper-Link -->
        <div *ngIf="component.type === 'link'" class="form-group">
          <label *ngIf="component.attributes.show_label">
            {{ component.attributes.label }}
          </label>
          <a
            [href]="component.attributes.url"
            target="_blank"
            rel="noopener noreferrer">
            {{ component.attributes.link_text }}
          </a>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
              type="file"

              accept=".jpg, .jpeg, .png"
              [id]="'file_' + component.attributes.field_name"
              style="display: none"
              (change)="onFileSelected($event, component)"
            />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- Image for guide  -->
        <div *ngIf="component.type === 'image'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>
          <img *ngIf="component.attributes.image_url" [src]="component.attributes.imageDisplayUrl" [alt]="component.attributes.alt_text" class="form-guide-image" />
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
              type="file"

              accept=".jpg, .jpeg, .png"
              [id]="'file_' + component.attributes.field_name"
              style="display: none"
              (change)="onFileSelected($event, component)"
            />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>

        <!-- QR CODE SCANNER -->
        <div *ngIf="component.type === 'qrscanner'" class="form-group">
          <label *ngIf="component.attributes.show_label">{{ component.attributes.label }}</label>

          <div class="scanner-input-wrapper">
            <input
              type="text"
              [formControlName]="component.attributes.field_name || ''"
              [placeholder]="component.attributes.placeholder_text || 'Enter or scan barcode'"
              class="scanner-input"
            />
            <button type="button" class="qr-icon-button" (click)="startScan(component)">
              <i class="fas fa-qrcode"></i>
            </button>
          </div>
          <div *ngIf="showScanner && currentScannerComponent === component" class="scanner-wrapper">
            <zxing-scanner
              [formats]="formats"
              (scanSuccess)="handleScanSuccess($event, component)"
              (scanError)="handleScanError($event)"
              (scanComplete)="handleScanComplete($event)"
              [enable]="showScanner"
            ></zxing-scanner>
            <button type="button" class="close-scanner-btn" (click)="stopScan()">Close Scanner</button>
          </div>

          <div *ngIf="formGroup.get(component.attributes.field_name || '')?.invalid && formGroup.get(component.attributes.field_name || '')?.touched" class="error-message">
            {{ getErrorMessage(component.attributes.field_name || '') }}
          </div>
          <div class="action-icons" *ngIf="component.attributes.actions">
            <a *ngIf="component.attributes.actions.comment" (click)="onComment(component)" title="Comment">
              <i class="fas fa-comment-alt"></i>
            </a>
            <a *ngIf="component.attributes.actions.camera" (click)="onCamera(component)" title="Upload Photo">
              <i class="fas fa-camera"></i>
            </a>
            <a *ngIf="component.attributes.actions.flag" (click)="onFlag(component)" title="Flag">
              <i class="fas fa-flag"></i>
            </a>
            <input
              type="file"

              accept=".jpg, .jpeg, .png"
              [id]="'file_' + component.attributes.field_name"
              style="display: none"
              (change)="onFileSelected($event, component)"
            />
          </div>
          <div class="uploaded-file-name" *ngIf="component.attributes.uploadedFileName">
            <img [src]="component.attributes.image_url" alt="Uploaded Image" style="max-width: 15vw; max-height: 30vh;" />
          </div>
            <!-- Commnet  -->
            <div *ngIf="visibleComments[component.attributes.field_name + '_comment']">
              <textarea
                [formControlName]="component.attributes.field_name + '_comment'"
                [rows]="3"
                placeholder="Add your comment"
                class="comment-box"
              ></textarea>
            </div>
        </div>


      </ng-container>
      <!-- Button -->
    </ng-container>
  </div>
  <div class="form-section" >
    <div class="btn-submission">
      <button class="form-button" type="submit">Submit</button>
      <button class="form-button" type="button" (click)="openManagerSignatureModal()" *ngIf="!managerSignatureSaved">Add Sign</button>
    </div>
  </div>

  <!-- Display Saved Manager Signatures -->
  <div class="form-section saved-signatures-section" *ngIf="managerSignatureSaved && managerSignatures.length > 0">
    <div class="saved-signatures-header">
      <h3><i class="fas fa-user-tie"></i> Manager Approvals ({{ managerSignatures.length }})</h3>
    </div>
    <div class="saved-signatures-content">
      <div class="signature-item" *ngFor="let signature of managerSignatures; let i = index">
        <div class="signature-header">
          <div class="signature-info">
            <h4><i class="fas fa-user"></i> {{ signature.name }}</h4>
            <p class="signature-timestamp"><i class="fas fa-clock"></i> {{ signature.timestamp }}</p>
          </div>
          <button
            type="button"
            class="remove-signature-btn"
            (click)="removeManagerSignature(signature.id)"
            *ngIf="!submissionId"
            title="Remove signature">
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div class="signature-display">
          <img [src]="signature.signature" alt="Manager Signature" class="signature-image" />
        </div>
      </div>
    </div>
  </div>

  <!-- Manager Signature Modal -->
  <div class="manager-signature-modal-overlay" *ngIf="showManagerSignatureModal" (click)="closeManagerSignatureModal()">
    <div class="manager-signature-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3><i class="fas fa-user-tie"></i> Manager Signature</h3>
        <button type="button" class="modal-close-btn" (click)="closeManagerSignatureModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        <div class="manager-name-input">
          <label for="modalManagerName">Manager Name *</label>
          <input
            type="text"
            id="modalManagerName"
            [(ngModel)]="tempManagerName"
            placeholder="Enter manager name"
            class="manager-input" />
        </div>

        <div class="signature-section">
          <label class="signature-label">Manager Signature *</label>
          <div class="signature-pad-container">
            <canvas #managerSignaturePad class="manager-signature-pad" width="500" height="200"></canvas>
            <div class="signature-controls">
              <div class="pen-color-control">
                <label for="managerPenColor">Pen Color:</label>
                <input type="color" id="managerPenColor" (change)="changeManagerPenColor($event)" [value]="managerSignatureColor" />
              </div>
              <div class="signature-buttons">
                <button type="button" class="clear-signature-btn" (click)="clearManagerSignature()">
                  <i class="fas fa-eraser"></i> Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="cancel-btn" (click)="closeManagerSignatureModal()">Cancel</button>
        <button type="button" class="save-btn" (click)="saveManagerSignature()">
          <i class="fas fa-save"></i> Save Signature
        </button>
      </div>
    </div>
  </div>


  </form>

    <!-- OneDrive Popup -->
    <div class="onedrive-popup-overlay" *ngIf="showOneDriveDialog">
      <div class="onedrive-popup">
        <div class="onedrive-popup-header">
          <h3>Save to OneDrive</h3>
          <div class="header-actions">
            <button class="account-btn" (click)="switchAccount()" title="Switch Microsoft Account">
              <i class="fas fa-user-circle"></i> Switch Account
            </button>
            <button class="close-btn" (click)="hideOneDrivePopup()">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <div class="onedrive-popup-content">
          <!-- Loading indicator -->
          <div *ngIf="isLoadingFolders" class="loading-indicator">
            <div class="spinner"></div>
            <p>Loading folders...</p>
          </div>

          <!-- Error message -->
          <div *ngIf="oneDriveError" class="error-messages">
            <i class="fas fa-exclamation-circle"></i> {{ oneDriveError }}
            <button (click)="loadOneDriveFolders()" class="retry-btn">Retry</button>
          </div>

          <!-- Folder list -->
          <div *ngIf="!isLoadingFolders && !oneDriveError" class="folder-list">
            <div class="folder-actions">
              <h4>Select a folder to save to:</h4>
              <button class="new-folder-btn" (click)="showNewFolderInput()">
                <i class="fas fa-folder-plus"></i> New Folder
              </button>
            </div>

            <!-- New folder input -->
            <div *ngIf="showingNewFolderInput" class="new-folder-input">
              <div class="new-folder-location">
                <span>Creating folder in: </span>
                <strong *ngIf="currentFolderId === 'Home'">Home</strong>
                <strong *ngIf="currentFolderId !== 'Home'">
                  {{ getCurrentFolderName() }}
                </strong>
              </div>
              <input type="text" [(ngModel)]="newFolderName" placeholder="Folder name"
                     (keyup.enter)="createNewFolder()">
              <div class="new-folder-buttons">
                <button (click)="createNewFolder()" [disabled]="!newFolderName">Create</button>
                <button (click)="cancelNewFolder()">Cancel</button>
              </div>
            </div>

            <!-- Current path -->
            <div class="folder-path" *ngIf="folderPath.length > 0">
              <span (click)="navigateToRoot()">Home</span>
              <span *ngFor="let pathItem of folderPath; let i = index">
                <i class="fas fa-chevron-right"></i>
                <span (click)="navigateToPathItem(i)">{{ pathItem.name }}</span>
              </span>
            </div>

            <div *ngIf="folders.length === 0" class="no-folders">
              <p>No folders found in this location</p>
            </div>

            <ul class="folder-tree">
              <li *ngFor="let folder of displayedFolders"
                  [class.selected]="selectedFolderId === folder.id"
                  [class.expanded]="folder.isExpanded">
                <div class="folder-item" (click)="selectFolder(folder)">
                  <span class="folder-expand" *ngIf="folder.isLoading">
                    <i class="fas fa-spinner fa-spin"></i>
                  </span>
                  <i class="fas fa-folder"></i>
                  <span class="folder-name">{{ folder.name }}</span>
                  <button class="folder-navigate-btn" (click)="$event.stopPropagation(); navigateToFolder(folder)" title="Open this folder">
                    <i class="fas fa-arrow-right"></i>
                  </button>
                </div>

                <!-- Subfolders -->
                <ul *ngIf="folder.isExpanded && folder.children && folder.children.length > 0" class="subfolder-list">
                  <li *ngFor="let subfolder of folder.children"
                      [class.selected]="selectedFolderId === subfolder.id">
                    <div class="subfolder-item" (click)="selectFolder(subfolder)">
                      <i class="fas fa-folder"></i>
                      <span class="folder-name">{{ subfolder.name }}</span>
                      <button class="folder-navigate-btn" (click)="$event.stopPropagation(); navigateToFolder(subfolder)" title="Open this folder">
                        <i class="fas fa-arrow-right"></i>
                      </button>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>

          <!-- Upload progress -->
          <div *ngIf="isUploading" class="upload-progress">
            <div class="progress-bar">
              <div class="progress" [style.width.%]="uploadProgress"></div>
            </div>
            <p>Uploading file... {{ uploadProgress }}%</p>
          </div>
        </div>

        <div class="onedrive-popup-footer">
          <button class="cancel-btn" (click)="hideOneDrivePopup()">Cancel</button>
          <button class="upload-btn"
                  [disabled]="!selectedFolderId || selectedFolderId === 'Home' || isUploading"
                  (click)="uploadToSelectedFolder()">
            <i class="fas fa-cloud-upload-alt"></i>
            <span *ngIf="selectedFolderId && selectedFolderId !== 'Home'" class="upload-path">Save to "{{ getSelectedFolderName() }}"</span>
            <span *ngIf="!selectedFolderId || selectedFolderId === 'Home'">Select a specific folder first</span>
          </button>
        </div>
      </div>
    </div>

</div>
