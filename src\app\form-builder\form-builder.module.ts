import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { FormBuilderRoutingModule } from './form-builder-routing.module';
import { FormBuilderComponent } from './Component/form-builder.component';
import { FormListComponent } from './Component/form-list.component';
import { FormSkeletonComponent } from '../SharedData/form-skeleton.component';
import { FormConfigComponent } from './Component/form-config.component';

@NgModule({
  declarations: [
    FormBuilderComponent,
    FormListComponent,
    FormConfigComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FormBuilderRoutingModule,
    FormSkeletonComponent
  ],
  exports: [

  ]
})
export class FormBuilderModule { }
